import os
import threading
from datetime import datetime, timedelta
import requests
import json


class YandexTokenManager:
    """
    a src to manage yandex iam tokens with automatic refresh.
    """

    def __init__(self, oauth_token=None, refresh_margin_minutes=2):
        """
        initialize the token manager.

        args:
            oauth_token: yandex oauth token. if none, will try to get from environment.
            refresh_margin_minutes: how many minutes before expiry to refresh the token
        """
        self.oauth_token = oauth_token or os.environ.get("YANDEX_0AUTH_TOKEN")
        if not self.oauth_token:
            raise ValueError(
                "no oauth token provided and yandex_0auth_token not set in environment"
            )

        self.refresh_margin = timedelta(minutes=refresh_margin_minutes)
        self.iam_token = None
        self.expiry_time = None
        self.lock = threading.RLock()
        self.refresh_thread = None

        # get initial token
        self.refresh_token()

    def get_token(self):
        """
        get the current iam token, refreshing if necessary.

        returns:
            current valid iam token
        """
        with self.lock:
            now = datetime.utcnow()

            # if token is expired or will expire soon, refresh it
            if (
                not self.iam_token
                or not self.expiry_time
                or now + self.refresh_margin >= self.expiry_time
            ):
                self.refresh_token()

            return self.iam_token

    def refresh_token(self):
        """
        force refresh the iam token.
        """
        with self.lock:
            url = "https://iam.api.cloud.yandex.net/iam/v1/tokens"

            headers = {"Content-Type": "application/json"}

            data = {"yandexPassportOauthToken": self.oauth_token}

            try:
                response = requests.post(url, headers=headers, data=json.dumps(data))
                response.raise_for_status()

                result = response.json()
                self.iam_token = result.get("iamToken")
                expires_at = result.get("expiresAt")

                if not self.iam_token or not expires_at:
                    raise ValueError(f"failed to get iam token from response: {result}")

                # parse expiry time (format may include microseconds)
                if "." in expires_at:
                    # handle format with microseconds: "2025-03-13T09:12:14.062625522Z"
                    expires_at = expires_at.split(".")[0] + "Z"

                self.expiry_time = datetime.strptime(expires_at, "%Y-%m-%dT%H:%M:%SZ")

                # update environment variable for other processes
                os.environ["YANDEX_IAM_TOKEN"] = self.iam_token

                # schedule next refresh
                self._schedule_refresh()

                return self.iam_token

            except Exception as e:
                print(f"error refreshing iam token: {str(e)}")
                if hasattr(e, "response") and e.response:
                    print(f"response: {e.response.text}")
                raise

    def _schedule_refresh(self):
        """
        schedule a token refresh before it expires.
        """
        if self.refresh_thread and self.refresh_thread.is_alive():
            self.refresh_thread.cancel()

        now = datetime.utcnow()
        if self.expiry_time:
            # calculate when to refresh (expiry time minus margin)
            refresh_at = self.expiry_time - self.refresh_margin

            # if that time is in the future, schedule a refresh
            if refresh_at > now:
                seconds_until_refresh = (refresh_at - now).total_seconds()

                self.refresh_thread = threading.Timer(
                    seconds_until_refresh, self.refresh_token
                )
                self.refresh_thread.daemon = True
                self.refresh_thread.start()


# example usage
if __name__ == "__main__":
    # create token manager
    try:
        token_manager = YandexTokenManager()

        # get token (will refresh if needed)
        token = token_manager.get_token()
        print(f"current iam token: {token}")

        # in your application, you would use token_manager.get_token()
        # whenever you need a fresh token

    except Exception as e:
        print(f"error: {str(e)}")
