#!/usr/bin/env python3
"""
test script to verify the corrected assemblyai integration works properly.
this script tests the basic functionality without requiring actual api calls.
"""

import os
import sys
import tempfile
from pathlib import Path

# add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.audio_processing.speech_to_text_assemblyai import SpeechToTextAssemblyAI
from src.utils.logger import logger


def test_assemblyai_initialization():
    """test that assemblyai stt can be initialized properly."""
    print("testing assemblyai initialization...")

    # temporarily remove environment variable to test fallback
    original_key = os.environ.get("ASSEMBLYAI_API_KEY")
    if "ASSEMBLYAI_API_KEY" in os.environ:
        del os.environ["ASSEMBLYAI_API_KEY"]

    try:
        # test without api key (should work in test mode)
        stt = SpeechToTextAssemblyAI(
            model_name="best",
            device="cpu",
            api_key=None,  # this should trigger test mode
            speaker_labels=True,
            language_detection=True,
        )

        assert (
            stt.api_key == "test_key"
        ), f"should default to test_key when no api key provided, got: {stt.api_key}"
        assert stt.speaker_labels == True, "speaker labels should be enabled"
        assert stt.language_detection == True, "language detection should be enabled"
        assert "best" in stt.speech_model_map, "should have speech model mapping"

        print("✅ assemblyai initialization test passed")

    finally:
        # restore original environment
        if original_key is not None:
            os.environ["ASSEMBLYAI_API_KEY"] = original_key


def test_assemblyai_load_model():
    """test that the load_model method works."""
    print("testing assemblyai load_model...")

    # temporarily remove environment variable to test fallback
    original_key = os.environ.get("ASSEMBLYAI_API_KEY")
    if "ASSEMBLYAI_API_KEY" in os.environ:
        del os.environ["ASSEMBLYAI_API_KEY"]

    try:
        stt = SpeechToTextAssemblyAI(api_key=None)
        stt.load_model()

        assert (
            stt.model == "assemblyai_best"
        ), "model should be set to assemblyai with model name"
        assert stt.transcriber is None, "transcriber should be none in test mode"

        print("✅ assemblyai load_model test passed")

    finally:
        # restore original environment
        if original_key is not None:
            os.environ["ASSEMBLYAI_API_KEY"] = original_key


def test_assemblyai_get_languages():
    """test that get_languages returns expected language list."""
    print("testing assemblyai get_languages...")

    stt = SpeechToTextAssemblyAI(api_key=None)
    languages = stt.get_languages()

    assert isinstance(languages, list), "should return a list"
    assert len(languages) > 0, "should return non-empty list"
    assert "eng" in languages, "should support english"
    assert "rus" in languages, "should support russian"
    assert "uzb" in languages, "should support uzbek"

    print(f"✅ assemblyai supports {len(languages)} languages")


def test_assemblyai_transcribe_test_mode():
    """test transcription in test mode."""
    print("testing assemblyai transcription in test mode...")

    # temporarily remove environment variable to test fallback
    original_key = os.environ.get("ASSEMBLYAI_API_KEY")
    if "ASSEMBLYAI_API_KEY" in os.environ:
        del os.environ["ASSEMBLYAI_API_KEY"]

    try:
        stt = SpeechToTextAssemblyAI(api_key=None)
        stt.load_model()

        # create a dummy audio file path (won't actually be used in test mode)
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_path = temp_file.name

        try:
            # test transcription (should return test message)
            result = stt._transcribe(
                vocals_filepath=temp_path, source_language_iso_639_1="en"
            )

            assert isinstance(result, str), "should return string"
            assert "test transcription" in result.lower(), "should return test message"

            print("✅ assemblyai transcription test mode passed")

        finally:
            # cleanup
            if os.path.exists(temp_path):
                os.remove(temp_path)

    finally:
        # restore original environment
        if original_key is not None:
            os.environ["ASSEMBLYAI_API_KEY"] = original_key


def test_assemblyai_language_detection_test_mode():
    """test language detection in test mode."""
    print("testing assemblyai language detection in test mode...")

    # temporarily remove environment variable to test fallback
    original_key = os.environ.get("ASSEMBLYAI_API_KEY")
    if "ASSEMBLYAI_API_KEY" in os.environ:
        del os.environ["ASSEMBLYAI_API_KEY"]

    try:
        stt = SpeechToTextAssemblyAI(api_key=None)
        stt.load_model()

        # create dummy audio array
        import array

        dummy_audio = array.array("h", [0] * 1000)  # 1000 samples of silence

        result = stt._get_audio_language(dummy_audio)

        assert isinstance(result, str), "should return string"
        assert result == "eng", "should default to english in test mode"

        print("✅ assemblyai language detection test mode passed")

    finally:
        # restore original environment
        if original_key is not None:
            os.environ["ASSEMBLYAI_API_KEY"] = original_key


def test_assemblyai_speaker_utterances_test_mode():
    """test speaker utterances in test mode."""
    print("testing assemblyai speaker utterances in test mode...")

    # temporarily remove environment variable to test fallback
    original_key = os.environ.get("ASSEMBLYAI_API_KEY")
    if "ASSEMBLYAI_API_KEY" in os.environ:
        del os.environ["ASSEMBLYAI_API_KEY"]

    try:
        stt = SpeechToTextAssemblyAI(api_key=None)
        stt.load_model()

        # create a dummy audio file path
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_path = temp_file.name

        try:
            result = stt.get_speaker_utterances(temp_path, "en")

            assert isinstance(result, list), "should return list"
            assert len(result) == 0, "should return empty list in test mode"

            print("✅ assemblyai speaker utterances test mode passed")

        finally:
            # cleanup
            if os.path.exists(temp_path):
                os.remove(temp_path)

    finally:
        # restore original environment
        if original_key is not None:
            os.environ["ASSEMBLYAI_API_KEY"] = original_key


def test_assemblyai_with_environment_variable():
    """test initialization with environment variable."""
    print("testing assemblyai with environment variable...")

    # temporarily set environment variable
    original_key = os.environ.get("ASSEMBLYAI_API_KEY")
    os.environ["ASSEMBLYAI_API_KEY"] = "test_env_key"

    try:
        stt = SpeechToTextAssemblyAI()
        assert stt.api_key == "test_env_key", "should use environment variable"

        print("✅ assemblyai environment variable test passed")

    finally:
        # restore original environment
        if original_key is not None:
            os.environ["ASSEMBLYAI_API_KEY"] = original_key
        else:
            os.environ.pop("ASSEMBLYAI_API_KEY", None)


def test_assemblyai_speech_models():
    """test speech model mapping."""
    print("testing assemblyai speech model mapping...")

    stt = SpeechToTextAssemblyAI(model_name="nano", api_key=None)

    assert "nano" in stt.speech_model_map, "should have nano model"
    assert "slam-1" in stt.speech_model_map, "should have slam-1 model"
    assert "universal" in stt.speech_model_map, "should have universal model"
    assert "best" in stt.speech_model_map, "should have best model"

    print("✅ assemblyai speech model mapping test passed")


def main():
    """run all tests."""
    print("🧪 running corrected assemblyai integration tests...\n")

    try:
        test_assemblyai_initialization()
        test_assemblyai_load_model()
        test_assemblyai_get_languages()
        test_assemblyai_transcribe_test_mode()
        test_assemblyai_language_detection_test_mode()
        test_assemblyai_speaker_utterances_test_mode()
        test_assemblyai_with_environment_variable()
        test_assemblyai_speech_models()

        print("\n🎉 all assemblyai integration tests passed!")
        print("\n📝 next steps:")
        print("1. set your assemblyai api key: export ASSEMBLYAI_API_KEY=your_key")
        print(
            "2. test with real audio: python -m src.core.main --input_file video.mp4 --target_language en --stt assemblyai"
        )
        print(
            "3. try different models: --stt assemblyai (uses 'best' model by default)"
        )
        print("4. available models: best, nano, slam-1, universal")

    except Exception as e:
        print(f"\n❌ test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
