import array
import os
import tempfile
import urllib.parse
import urllib.request

import assemblyai as aai
from pydub import AudioSegment

from src.audio_processing.speech_to_text import SpeechToText
from src.utils.logger import logger


class SpeechToTextAssemblyAI(SpeechToText):
    """assemblyai speech-to-text implementation with advanced features."""

    def __init__(
        self,
        *,
        model_name="best",  # assemblyai speech model (best, nano, slam-1, universal)
        device="cpu",  # assemblyai is cloud-based, device doesn't matter
        cpu_threads=0,  # not applicable for cloud service
        api_key: str | None = None,
        speaker_labels: bool = True,  # enable speaker diarization
        auto_highlights: bool = False,  # extract key phrases
        sentiment_analysis: bool = False,  # analyze sentiment
        entity_detection: bool = False,  # detect entities
        language_detection: bool = True,  # auto-detect language
    ):
        super().__init__(device=device, model_name=model_name, cpu_threads=cpu_threads)

        # get api key from parameter or environment
        self.api_key = api_key or os.environ.get("ASSEMBLYAI_API_KEY")
        if not self.api_key:
            logger().warning(
                "no assemblyai api key found. set assemblyai_api_key environment variable "
                "or pass api_key parameter. running in test mode."
            )
            self.api_key = "test_key"

        # configure assemblyai sdk
        if self.api_key != "test_key":
            aai.settings.api_key = self.api_key

        # assemblyai-specific features
        self.speaker_labels = speaker_labels
        self.auto_highlights = auto_highlights
        self.sentiment_analysis = sentiment_analysis
        self.entity_detection = entity_detection
        self.language_detection = language_detection

        # map model names to assemblyai speech models
        self.speech_model_map = {
            "best": aai.SpeechModel.best,
            "nano": aai.SpeechModel.nano,
            "slam-1": aai.SpeechModel.slam_1,
            "universal": aai.SpeechModel.universal,
        }

        # supported languages (assemblyai supports many languages)
        self._supported_languages = [
            "eng",
            "spa",
            "fra",
            "deu",
            "ita",
            "por",
            "rus",
            "jpn",
            "kor",
            "zho",
            "ara",
            "hin",
            "tur",
            "pol",
            "nld",
            "swe",
            "dan",
            "nor",
            "fin",
            "ces",
            "hun",
            "ron",
            "bul",
            "hrv",
            "slk",
            "slv",
            "est",
            "lav",
            "lit",
            "mlt",
            "ell",
            "heb",
            "tha",
            "vie",
            "ind",
            "msa",
            "tgl",
            "ukr",
            "bel",
            "kaz",
            "uzb",
            "aze",
            "kat",
            "hye",
            "fas",
            "urd",
            "ben",
            "guj",
            "pan",
            "tam",
            "tel",
            "kan",
            "mal",
            "ori",
            "mar",
            "nep",
            "sin",
            "mya",
            "khm",
            "lao",
        ]

        logger().info(
            f"initialized assemblyai stt with features: speaker_labels={speaker_labels}"
        )

    def load_model(self):
        """assemblyai is cloud-based, no local model loading required."""
        try:
            if self.api_key != "test_key":
                # verify api key by creating a transcriber instance
                self.transcriber = aai.Transcriber()
                logger().info("assemblyai api connection verified successfully")
            else:
                logger().warning(
                    "running assemblyai in test mode - api calls will be mocked"
                )
                self.transcriber = None

            self._model = f"assemblyai_{self.model_name}"

        except Exception as e:
            logger().error(f"failed to initialize assemblyai: {e}")
            raise

    def get_languages(self):
        """return list of supported languages in iso 639-3 format."""
        return self._supported_languages.copy()

    def detect_language(self, filename: str) -> str:
        """detect language from audio/video file, handling both local files and urls."""
        duration_secs = 30

        # handle urls by downloading them to a temporary file
        temp_file = None
        actual_filename = filename

        if filename.startswith(("http://", "https://", "ftp://", "ftps://")):
            logger().info(
                f"downloading video from url for language detection: {filename}"
            )

            # create a temporary file
            parsed_url = urllib.parse.urlparse(filename)
            file_ext = os.path.splitext(parsed_url.path)[1] or ".mp4"
            temp_file = tempfile.NamedTemporaryFile(suffix=file_ext, delete=False)
            temp_file.close()

            try:
                # download the file
                urllib.request.urlretrieve(filename, temp_file.name)
                actual_filename = temp_file.name
                logger().info(f"downloaded video to temporary file: {actual_filename}")
            except Exception as e:
                logger().error(f"failed to download video from url: {e}")
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)
                raise

        try:
            audio = AudioSegment.from_file(actual_filename)
            audio = audio.set_channels(1)
            audio = audio.set_frame_rate(16000)

            first_seconds = audio[: duration_secs * 1000].get_array_of_samples()
            return self._get_audio_language(first_seconds)
        finally:
            # clean up temporary file if it was created
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                    logger().info(f"cleaned up temporary file: {temp_file.name}")
                except Exception as e:
                    logger().warning(f"failed to clean up temporary file: {e}")

    def _transcribe(
        self,
        *,
        vocals_filepath: str,
        source_language_iso_639_1: str,
    ) -> str:
        """transcribe audio file using assemblyai."""
        if self.api_key == "test_key":
            logger().warning("using test mode - returning placeholder transcription")
            return "this is a test transcription from assemblyai"

        try:
            # get the speech model to use
            speech_model = self.speech_model_map.get(
                self.model_name, aai.SpeechModel.best
            )

            # configure transcription settings based on assemblyai api
            config = aai.TranscriptionConfig(
                speech_model=speech_model,
                language_code=(
                    source_language_iso_639_1 if not self.language_detection else None
                ),
                language_detection=self.language_detection,
                speaker_labels=self.speaker_labels,
                auto_highlights=self.auto_highlights,
                sentiment_analysis=self.sentiment_analysis,
                entity_detection=self.entity_detection,
                punctuate=True,
                format_text=True,
            )

            transcriber = aai.Transcriber(config=config)

            logger().info(f"starting assemblyai transcription for: {vocals_filepath}")
            transcript = transcriber.transcribe(vocals_filepath)

            # the sdk handles polling automatically, but we can check status
            if transcript.status == aai.TranscriptStatus.error:
                logger().error(f"assemblyai transcription failed: {transcript.error}")
                return ""

            logger().info("assemblyai transcription completed successfully")

            # store additional data for potential future use
            if hasattr(transcript, "utterances") and transcript.utterances:
                logger().debug(
                    f"transcript contains {len(transcript.utterances)} utterances"
                )

            # ensure we have text before processing
            text = transcript.text or ""
            return self._make_sure_single_space(text)

        except Exception as e:
            logger().error(f"assemblyai transcription error: {e}")
            # fallback to empty string to prevent pipeline failure
            return ""

    def _get_audio_language(self, audio: array.array) -> str:
        """detect language from audio sample using assemblyai."""
        if self.api_key == "test_key":
            logger().warning("using test mode - returning default language")
            return "eng"

        try:
            # convert audio array to temporary file for assemblyai
            audio_segment = AudioSegment(
                audio.tobytes(), frame_rate=16000, sample_width=2, channels=1
            )

            # create temporary file
            temp_file = "temp_language_detection.wav"
            audio_segment.export(temp_file, format="wav")

            try:
                config = aai.TranscriptionConfig(
                    language_detection=True,
                    speaker_labels=False,
                )

                transcriber = aai.Transcriber(config=config)
                transcript = transcriber.transcribe(temp_file)

                if transcript.status == aai.TranscriptStatus.completed:
                    # assemblyai returns language detection in the transcript
                    detected_language = getattr(transcript, "language_code", None)
                    if detected_language:
                        # convert iso 639-1 to iso 639-3
                        iso_639_3 = self._get_iso_639_3(detected_language)
                        logger().debug(f"assemblyai detected language: {iso_639_3}")
                        return iso_639_3
                else:
                    logger().warning(
                        "assemblyai language detection failed, defaulting to english"
                    )
                    return "eng"

            finally:
                # cleanup temporary file
                if os.path.exists(temp_file):
                    os.remove(temp_file)

            # fallback if no language detected
            return "eng"

        except Exception as e:
            logger().error(f"assemblyai language detection error: {e}")
            return "eng"  # fallback to english

    def get_speaker_utterances(
        self, vocals_filepath: str, source_language_iso_639_1: str
    ):
        """get detailed utterance information with speaker labels from assemblyai."""
        if self.api_key == "test_key":
            logger().warning("speaker utterances not available in test mode")
            return []

        try:
            # get the speech model to use
            speech_model = self.speech_model_map.get(
                self.model_name, aai.SpeechModel.best
            )

            config = aai.TranscriptionConfig(
                speech_model=speech_model,
                language_code=(
                    source_language_iso_639_1 if not self.language_detection else None
                ),
                language_detection=self.language_detection,
                speaker_labels=True,  # force speaker labels for this method
                punctuate=True,
                format_text=True,
            )

            transcriber = aai.Transcriber(config=config)
            transcript = transcriber.transcribe(vocals_filepath)

            if (
                transcript.status == aai.TranscriptStatus.completed
                and transcript.utterances
            ):
                utterances = []
                for utterance in transcript.utterances:
                    utterances.append(
                        {
                            "speaker": utterance.speaker,
                            "text": self._make_sure_single_space(utterance.text),
                            "start": utterance.start / 1000.0,  # convert ms to seconds
                            "end": utterance.end / 1000.0,
                            "confidence": getattr(utterance, "confidence", 0.9),
                        }
                    )

                logger().info(f"extracted {len(utterances)} speaker utterances")
                return utterances
            else:
                logger().warning("no speaker utterances available from assemblyai")
                return []

        except Exception as e:
            logger().error(f"error getting speaker utterances: {e}")
            return []
