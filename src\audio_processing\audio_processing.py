import os
import warnings

from typing import Final, Mapping, Sequence

import numpy as np
import torch

from moviepy import AudioFileClip
from pyannote.audio import Pipeline
from pydub.silence import detect_nonsilent
from pydub import AudioSegment as PydubAudioSegment

from src.utils.logger import logger
from src.audio_processing.pydub_audio_segment import AudioSegment

_DEFAULT_DUBBED_VOCALS_AUDIO_FILE: Final[str] = "dubbed_vocals.mp3"
_DEFAULT_DUBBED_AUDIO_FILE: Final[str] = "dubbed_audio"
_DEFAULT_OUTPUT_FORMAT: Final[str] = ".mp3"

# add these constants for pause detection
_DEFAULT_SILENCE_THRESHOLD: Final[float] = -35  # db
_DEFAULT_MIN_SILENCE_DURATION: Final[float] = 0.5  # seconds
_DEFAULT_MAX_SEGMENT_DURATION: Final[float] = 30.0  # seconds


def create_pyannote_timestamps(
    *,
    audio_file: str,
    pipeline: Pipeline,
    device: str = "cpu",
) -> Sequence[Mapping[str, float]]:
    """creates timestamps from a vocals file using pyannote speaker diarization.

    returns:
        a list of dictionaries containing start and end timestamps for each
        speaker segment.
    """
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=UserWarning)
        if device == "cuda":
            pipeline.to(torch.device("cuda"))
        diarization = pipeline(audio_file)
        utterance_metadata = [
            {"start": segment.start, "end": segment.end, "speaker_id": speaker}
            for segment, _, speaker in diarization.itertracks(yield_label=True)
        ]
        return utterance_metadata


def _cut_and_save_audio(
    *,
    audio: AudioSegment,
    utterance: Mapping[str, str | float],
    prefix: str,
    output_directory: str,
) -> str:
    """cuts a specified segment from an audio file, saves it as an mp3, and returns the path of the saved file.

    args:
        audio: the audio file from which to extract the segment.
        utterance: a dictionary containing the start and end times of the segment
          to be cut. - 'start': the start time of the segment in seconds. - 'end':
          the end time of the segment in seconds.
        prefix: a string to be used as a prefix in the filename of the saved audio
          segment.
        output_directory: the directory path where the cut audio segment will be
          saved.

    returns:
        the path of the saved mp3 file.
    """
    start_time_ms = int(utterance["start"] * 1000)
    end_time_ms = int(utterance["end"] * 1000)
    chunk = audio[start_time_ms:end_time_ms]
    chunk_filename = f"{prefix}_{utterance['start']}_{utterance['end']}.mp3"
    chunk_path = os.path.join(output_directory, chunk_filename)
    chunk.export(chunk_path, format="mp3")
    return chunk_path


def run_cut_and_save_audio(
    *,
    utterance_metadata: Sequence[Mapping[str, float]],
    audio_file: str,
    output_directory: str,
) -> Sequence[Mapping[str, float]]:
    """cuts an audio file into chunks based on provided time ranges and saves each chunk to a file.

    returns:
        a list of dictionaries, each containing the path to the saved chunk, and
        the original start and end times.
    """

    audio = AudioSegment.from_file(audio_file)
    key = "path"
    prefix = "chunk"
    updated_utterance_metadata = []
    for utterance in utterance_metadata:
        chunk_path = _cut_and_save_audio(
            audio=audio,
            utterance=utterance,
            prefix=prefix,
            output_directory=output_directory,
        )
        utterance_copy = utterance.copy()
        utterance_copy[key] = chunk_path
        updated_utterance_metadata.append(utterance_copy)
    return updated_utterance_metadata


def insert_audio_at_timestamps(
    *,
    utterance_metadata: Sequence[Mapping[str, str | float]],
    background_audio_file: str,
    output_directory: str,
) -> str:
    """inserts audio chunks into a background audio track at specified timestamps."""
    # use from_file instead of from_mp3 to handle different audio formats (wav, mp3, etc.)
    background_audio = AudioSegment.from_file(background_audio_file)
    total_duration = background_audio.duration_seconds
    logger().debug(
        f"insert_audio_at_timestamps: background_audio_file={background_audio_file}, duration={total_duration}s"
    )
    output_audio = AudioSegment.silent(duration=total_duration * 1000)
    for item in utterance_metadata:
        _file = ""
        try:
            for_dubbing = item["for_dubbing"]
            _file = item["dubbed_path"]

            if for_dubbing is False:
                start = int(item["start"])
                end = int(item["end"])
                logger().debug(
                    f"insert_audio_at_timestamps. skipping {_file} at start time {start} and end at {end}"
                )
                continue

            start_time = int(item["start"] * 1000)
            logger().debug(f"insert_audio_at_timestamps. open: {_file}")
            audio_chunk = AudioSegment.from_mp3(_file)
            output_audio = output_audio.overlay(
                audio_chunk, position=start_time, loop=False
            )
        except Exception as e:
            start = int(item["start"])
            end = int(item["end"])
            logger().error(
                f"insert_audio_at_timestamps. error on file: {_file} at start time {start} and end at {end}, error: {e}"
            )

    dubbed_vocals_audio_file = os.path.join(
        output_directory, _DEFAULT_DUBBED_VOCALS_AUDIO_FILE
    )
    output_audio.export(dubbed_vocals_audio_file, format="mp3")
    return dubbed_vocals_audio_file


def _needs_background_normalization(
    *, background_audio_file: str, threshold: float = 0.1
):
    try:
        chunk_size = 1024
        fps = 44100

        clip = AudioFileClip(background_audio_file)
        duration = clip.duration
        num_chunks = int(duration * fps / chunk_size)

        max_amplitude = 0

        for i in range(num_chunks):
            start = i * chunk_size / fps
            end = (i + 1) * chunk_size / fps
            audio_chunk = clip.subclipped(start, end).to_soundarray(fps=fps)

            # calculate maximum amplitude of this chunk
            chunk_amplitude = np.abs(audio_chunk).max(axis=1).max()
            max_amplitude = max(max_amplitude, chunk_amplitude)

        needs = max_amplitude > threshold
        logger().debug(
            f"_needs_background_normalization. max_amplitude: {max_amplitude}, needs {needs}"
        )
        return needs, max_amplitude

    except Exception as e:
        logger().error(f"_needs_background_normalization. error: {e}")
        return True, 1.0

    finally:
        clip.close()


def merge_background_and_vocals(
    *,
    background_audio_file: str,
    dubbed_vocals_audio_file: str,
    output_directory: str,
    target_language: str,
    vocals_volume_adjustment: float = 5.0,
    background_volume_adjustment: float = 0.0,
) -> str:
    """mixes background music and vocals tracks, normalizes the volume, and exports the result.

    returns:
      the path to the output audio file with merged dubbed vocals and original
      background audio.
    """
    try:
        # use from_file to handle different audio formats (wav, mp3, etc.)
        background = AudioSegment.from_file(background_audio_file)
        vocals = AudioSegment.from_file(dubbed_vocals_audio_file)

        # log the audio file details for debugging
        logger().info(f"background audio: duration={background.duration_seconds:.2f}s")
        logger().info(f"vocals audio: duration={vocals.duration_seconds:.2f}s")

        # if background normalization is not needed, we skip it since it sometimes raises up
        # residuals vocals not properly split in the demucs processes
        needs, max_amplitude = _needs_background_normalization(
            background_audio_file=background_audio_file
        )

        # safer normalization with error handling
        try:
            if needs:
                logger().info(
                    f"merge_background_and_vocals. normalizing background (max amplitude {max_amplitude:.2f})"
                )
                background = background.normalize()
        except Exception as e:
            logger().warning(
                f"background normalization failed: {str(e)}. continuing without normalization."
            )
            # don't re-raise, continue with unnormalized background

        # safer vocals normalization
        try:
            vocals = vocals.normalize()
        except Exception as e:
            logger().warning(
                f"vocals normalization failed: {str(e)}. continuing without normalization."
            )
            # don't re-raise, continue with unnormalized vocals

        # apply volume adjustments
        background = background + background_volume_adjustment
        vocals = vocals + vocals_volume_adjustment

        # ensure lengths match before overlay to prevent issues
        shortest_length = min(len(background), len(vocals))
        logger().info(f"shortest audio length: {shortest_length/1000:.2f}s")

        # ensure we have valid audio lengths
        if shortest_length <= 0:
            logger().error("invalid audio length detected")
            raise ValueError("one or both audio files have invalid length")

        background = background[:shortest_length]
        vocals = vocals[:shortest_length]

        # create the mixed audio
        mixed_audio = background.overlay(vocals)

        # prepare output path and export
        target_language_suffix = "_" + target_language.replace("-", "_").lower()
        dubbed_audio_file = os.path.join(
            output_directory,
            _DEFAULT_DUBBED_AUDIO_FILE
            + target_language_suffix
            + _DEFAULT_OUTPUT_FORMAT,
        )

        # log and export
        logger().info(f"exporting mixed audio to: {dubbed_audio_file}")
        mixed_audio.export(dubbed_audio_file, format="mp3")

        # verify the exported file
        if not os.path.exists(dubbed_audio_file):
            logger().error(
                f"export failed: output file is missing: {dubbed_audio_file}"
            )
            raise ValueError("export produced invalid file")

        file_size = os.path.getsize(dubbed_audio_file)
        if file_size < 100:  # reduced threshold for very short audio clips
            logger().error(
                f"export failed: output file too small ({file_size} bytes): {dubbed_audio_file}"
            )
            raise ValueError("export produced invalid file")

        return dubbed_audio_file

    except Exception as e:
        logger().error(f"error in merge_background_and_vocals: {str(e)}")
        raise  # re-raise to be handled by calling function


def detect_natural_pauses(
    *,
    audio_file: str,
    min_silence_duration: float = _DEFAULT_MIN_SILENCE_DURATION,
    silence_threshold: float = _DEFAULT_SILENCE_THRESHOLD,
    max_segment_duration: float = _DEFAULT_MAX_SEGMENT_DURATION,
) -> Sequence[Mapping[str, float]]:
    """detects natural pauses in speech to improve segmentation."""
    logger().debug(f"detecting natural pauses in {audio_file}")

    try:
        # load the audio file with standard pydub (not our custom implementation)
        # to ensure we have access to all pydub methods needed for silence detection
        audio = PydubAudioSegment.from_file(audio_file)
        audio_duration_ms = len(audio)
        audio_duration = audio_duration_ms / 1000.0  # convert to seconds

        # convert parameters to milliseconds for pydub
        min_silence_ms = int(min_silence_duration * 1000)

        # detect non-silent parts using silence_threshold
        non_silent_ranges = detect_nonsilent(
            audio, min_silence_len=min_silence_ms, silence_thresh=silence_threshold
        )

        if not non_silent_ranges:
            logger().warning(
                "no speech detected in audio, falling back to fixed segmentation"
            )
            return _create_fixed_segments(audio_duration, max_segment_duration)

        # create segments based on natural pauses
        segments = []
        current_start = 0
        current_end = 0

        for start_ms, end_ms in non_silent_ranges:
            # convert to seconds
            start = start_ms / 1000.0
            end = end_ms / 1000.0

            # if this speech segment starts after the current segment ends
            # then there's a pause between them
            if start > current_end + min_silence_duration:
                # if we have an active segment, finish it
                if current_end > current_start:
                    segments.append({"start": current_start, "end": current_end})
                    # start a new segment
                    current_start = start
                    current_end = end
                else:
                    # first segment or after a reset
                    current_start = start
                    current_end = end
            else:
                # extend the current segment
                current_end = end

            # if the current segment exceeds max_segment_duration, cut it
            if current_end - current_start > max_segment_duration:
                # end the segment at max_segment_duration from start
                segment_end = current_start + max_segment_duration
                segments.append({"start": current_start, "end": segment_end})

                # start a new segment from where we cut
                current_start = segment_end

        # add the final segment if there's one in progress
        if current_end > current_start:
            segments.append({"start": current_start, "end": current_end})

        # ensure we cover any gaps at the end
        if segments and segments[-1]["end"] < audio_duration:
            last_end = segments[-1]["end"]
            remaining_duration = audio_duration - last_end

            # only add if there's a significant amount remaining
            if remaining_duration > 0.5:
                if remaining_duration <= max_segment_duration:
                    segments.append({"start": last_end, "end": audio_duration})
                else:
                    # if remaining is too long, segment it
                    start = last_end
                    while start < audio_duration:
                        end = min(start + max_segment_duration, audio_duration)
                        segments.append({"start": start, "end": end})
                        start = end

        # check for empty result and fall back if needed
        if not segments:
            logger().warning("no segments created, falling back to fixed segmentation")
            return _create_fixed_segments(audio_duration, max_segment_duration)

        logger().info(
            f"created {len(segments)} segments based on natural pauses: {segments}"
        )
        return segments

    except Exception as e:
        logger().error(f"error detecting natural pauses: {str(e)}")
        logger().info("using fixed segmentation as fallback")

        # get audio duration for fallback
        try:
            audio = PydubAudioSegment.from_file(audio_file)
            audio_duration_ms = len(audio)
            audio_duration = audio_duration_ms / 1000.0
        except Exception:
            # if we can't even get the duration, use a default
            audio_duration = 30.0

        return _create_fixed_segments(audio_duration, max_segment_duration)


def _create_fixed_segments(
    audio_duration: float, max_segment_duration: float
) -> Sequence[Mapping[str, float]]:
    """helper function to create fixed-length segments."""
    segments = []
    start = 0

    while start < audio_duration:
        end = min(start + max_segment_duration, audio_duration)
        segments.append({"start": start, "end": end})
        start = end

    logger().info(
        f"created {len(segments)} uniform segments of max {max_segment_duration}s"
    )
    return segments


def combine_diarization_and_pauses(
    *,
    diarization_segments: Sequence[Mapping[str, str | float]],
    pause_segments: Sequence[Mapping[str, float]],
    max_segment_duration: float = _DEFAULT_MAX_SEGMENT_DURATION,
) -> Sequence[Mapping[str, str | float]]:
    """combines speaker diarization and natural pause segments.

    args:
        diarization_segments: speaker diarization segments
        pause_segments: natural pause segments
        max_segment_duration: maximum allowed segment duration

    returns:
        combined segments respecting both speaker changes and natural pauses.
    """
    # build combined timeline
    all_breakpoints = set()

    # add all segment boundaries from both methods
    for segment in diarization_segments:
        all_breakpoints.add(segment["start"])
        all_breakpoints.add(segment["end"])

    for segment in pause_segments:
        all_breakpoints.add(segment["start"])
        all_breakpoints.add(segment["end"])

    # sort breakpoints
    timeline = sorted(all_breakpoints)

    # create new segments based on combined breakpoints
    combined_segments = []
    for i in range(len(timeline) - 1):
        start_time = timeline[i]
        end_time = timeline[i + 1]

        # skip very small segments (less than 0.1 seconds)
        if end_time - start_time < 0.1:
            continue

        # find which speaker is active during this segment
        speaker_id = None
        for seg in diarization_segments:
            # if this diarization segment overlaps significantly with our new segment
            if seg["start"] <= start_time and seg["end"] >= end_time:
                speaker_id = seg.get("speaker_id", "UNKNOWN")
                break

        # if we couldn't find a speaker, use the speaker of the nearest segment
        if speaker_id is None:
            nearest_distance = float("inf")
            for seg in diarization_segments:
                # calculate distance to segment
                if start_time < seg["start"]:
                    distance = seg["start"] - start_time
                elif end_time > seg["end"]:
                    distance = end_time - seg["end"]
                else:
                    # we're inside the segment
                    distance = 0

                if distance < nearest_distance:
                    nearest_distance = distance
                    speaker_id = seg.get("speaker_id", "UNKNOWN")

        combined_segments.append(
            {"start": start_time, "end": end_time, "speaker_id": speaker_id}
        )

    # merge adjacent segments with the same speaker that are less than max_segment_duration
    merged_segments = []
    current_segment = None

    for segment in combined_segments:
        if current_segment is None:
            current_segment = segment.copy()
        elif (
            segment["speaker_id"] == current_segment["speaker_id"]
            and segment["end"] - current_segment["start"] <= max_segment_duration
        ):
            # extend current segment
            current_segment["end"] = segment["end"]
        else:
            # different speaker or too long, add current segment and start a new one
            merged_segments.append(current_segment)
            current_segment = segment.copy()

    # add the last segment
    if current_segment is not None:
        merged_segments.append(current_segment)

    return merged_segments
