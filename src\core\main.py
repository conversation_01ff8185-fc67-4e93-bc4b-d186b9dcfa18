import logging
import os
import sys
import warnings
import signal
import threading
import platform

import transformers

from iso639 import <PERSON>

from src.utils.logger import logger
from src.core.command_line import CommandLine
from src.core.dubbing import Dubber
from src.utils.exit_code import ExitCode
from src.video_processing.ffmpeg import <PERSON><PERSON><PERSON>, HWAccel
from src.video_processing.video_processing import VideoProcessing
from src.audio_processing.speech_to_text_faster_whisper import SpeechToTextFasterWhisper
from src.audio_processing.speech_to_text_assemblyai import SpeechToTextAssemblyAI
from src.translation.translation_gemini import GeminiTranslation
from src.audio_processing.text_to_speech_yandex import TextToSpeechYandex
from src.audio_processing.text_to_speech_azure import TextToSpeechAzure
from src.audio_processing.text_to_speech_elevenlabs import TextToSpeechElevenLabs
from src.utils.run_manager import RunManager


from dotenv import load_dotenv
from pathlib import Path

# load environment variables from .env file in project root
project_root = Path(__file__).parent.parent.parent
env_file = project_root / ".env"
load_dotenv(env_file)

# global flag to indicate shutdown is in progress
shutdown_in_progress = False
# global variable to store the current dubber instance
current_dubber = None
# lock for thread-safe operations
shutdown_lock = threading.Lock()

# configure windows console for proper unicode support
if platform.system() == "Windows":
    try:
        # try to set console to utf-8 mode on windows
        import ctypes

        kernel32 = ctypes.windll.kernel32
        # set console output code page to utf-8
        kernel32.SetConsoleOutputCP(65001)
        # set console input code page to utf-8
        kernel32.SetConsoleCP(65001)
        os.system("")  # enable ansi sequences
    except Exception as e:
        # don't fail if we can't set the code page
        print(f"warning: could not set console code page to utf-8: {str(e)}")


def handle_shutdown_signal(signum, frame):
    """
    handle shutdown signals (ctrl+c, etc) gracefully
    """
    global shutdown_in_progress, current_dubber

    with shutdown_lock:
        if shutdown_in_progress:
            return
        shutdown_in_progress = True

        logger().info("\nshutdown signal received. cleaning up...")

    try:
        if current_dubber:
            current_dubber.cleanup()
    except Exception as e:
        logger().error(f"error during cleanup: {e}")
    finally:
        logger().info("shutdown complete")
        sys.exit(0)


def _init_logging(log_level, log_format: str | None = None):
    """
    initialize logging settings for the application.
    """
    # convert string log level to logging constant
    level_map = {
        "debug": logging.DEBUG,
        "info": logging.INFO,
        "warning": logging.WARNING,
        "error": logging.ERROR,
        "critical": logging.CRITICAL,
    }
    numeric_level = level_map.get(log_level.lower(), logging.INFO)

    # set the log level for the dubbing logger
    app_logger = logger()
    app_logger.setLevel(numeric_level)

    # if a custom format string is provided, update existing handlers
    if log_format is not None:
        for h in app_logger.handlers:
            if isinstance(h.formatter, logging.Formatter):
                h.setFormatter(
                    h.formatter.__class__(
                        fmt=log_format.replace(
                            "%(levelname)s", "%(color)s%(levelname)s%(reset)s"
                        ),
                        datefmt="%Y-%m-%d %H:%M:%S",
                    )
                )

    # suppress third-party loggers
    for name in logging.root.manager.loggerDict:
        if name not in {"dubbing_logger", "root"}:
            logging.getLogger(name).setLevel(logging.ERROR)

    transformers.logging.set_verbosity_error()
    warnings.filterwarnings("ignore", category=FutureWarning)

    # suppress speechbrain deprecation warning
    warnings.filterwarnings(
        "ignore",
        message="module 'speechbrain.pretrained' was deprecated",
        category=UserWarning,
    )


def log_error_and_exit(msg: str, code: ExitCode):
    logger().error(msg)
    exit(code)


def check_languages(
    source_language, target_language, _tts, translation, _stt, target_language_region
):
    """
    check if the specified source and target languages are supported by the
    speech-to-text, translation, and text-to-speech systems.
    """
    spt = _stt.get_languages()

    # gemini can handle any language pair, so we don't need to check translation_languages
    # we just handle special cases for clarity
    logger().debug("using gemini for all translation needs")

    tts = _tts.get_languages()

    if source_language not in spt:
        msg = f"source language '{source_language}' is not supported by the speech recognition system. supported languages: '{spt}'"
        log_error_and_exit(msg, ExitCode.INVALID_LANGUAGE_SPT)

    # special case for uzbek
    if target_language == "uz" or target_language == "uzb":
        # skip the translation language pair check for uzbek
        logger().info(
            "using special handling for uzbek language translation with gemini"
        )
    # special case for russian to english
    elif (source_language == "rus" or source_language == "ru") and (
        target_language == "en" or target_language == "eng"
    ):
        # skip the translation language pair check for russian to english
        logger().info(
            "using special handling for russian to english translation with gemini"
        )
    else:
        # gemini can handle any language pair
        logger().info(
            f"using gemini for translation from {source_language} to {target_language}"
        )

    if target_language not in tts:
        msg = f"target language '{target_language}' is not supported by the text to speech system. supported languages: '{tts}'"
        log_error_and_exit(msg, ExitCode.INVALID_LANGUAGE_TTS)

    voices = _tts.get_available_voices(language_code=target_language)
    region_voices = _tts.get_voices_for_region_only(
        voices=voices, target_language_region=target_language_region
    )
    if len(region_voices) == 0:
        msg = f"filtering by '{target_language_region}' returns no voices for language '{target_language}' in the text to speech system"
        log_error_and_exit(msg, ExitCode.INVALID_LANGUAGE_TTS)


_ACCEPTED_VIDEO_FORMATS = ["mp4"]


def check_is_a_video(input_file: str):
    """
    check if the input file has an accepted video format.
    handles both local files and urls.
    """
    # for urls, extract the file extension from the path part
    if input_file.startswith(("http://", "https://", "ftp://", "ftps://")):
        import urllib.parse

        parsed_url = urllib.parse.urlparse(input_file)
        _, file_extension = os.path.splitext(parsed_url.path)
    else:
        # for local files, use the standard approach
        _, file_extension = os.path.splitext(input_file)

    file_extension = file_extension.lower().lstrip(".")

    if file_extension in _ACCEPTED_VIDEO_FORMATS:
        return
    msg = f"unsupported file format: {file_extension}"
    log_error_and_exit(msg, ExitCode.INVALID_FILEFORMAT)


HUGGING_FACE_VARNAME = "HF_TOKEN"


def get_token(provided_token: str) -> str:
    """
    retrieve the hugging face token from provided argument or environment variable.
    """
    token = provided_token or os.getenv(HUGGING_FACE_VARNAME)
    if not token:
        msg = "you must either provide the '--hugging_face_token' argument or"
        msg += f" set the '{HUGGING_FACE_VARNAME.upper()}' environment variable."
        log_error_and_exit(msg, ExitCode.MISSING_HF_KEY)
    return token


def _get_language_names(languages_iso_639_3):
    """
    convert a list of iso 639-3 language codes to their full names.
    """
    names = []
    for language in languages_iso_639_3:
        o = Lang(language)
        names.append(o.name)
    return sorted(names)


def list_supported_languages(_tts, translation, device):  # todo: not used
    """
    list all supported source and target languages across stt, translation, and tts.
    """
    s = SpeechToTextFasterWhisper(device=device)
    s.load_model()
    spt = s.get_languages()
    trans = translation.get_languages()
    tts = _tts.get_languages()

    source = _get_language_names(set(spt).intersection(set(trans)))
    print(f"supported source languages: {source}")

    target = _get_language_names(set(tts).intersection(set(trans)))
    print(f"supported target languages: {target}")


def _get_selected_tts(
    selected_tts: str,
    _device: str,
):
    """
    initialize and return the selected text-to-speech (tts) service.
    """
    if selected_tts == "yandex":
        # get yandex credentials from environment variables
        yandex_iam_token = os.environ.get("yandex_iam_token")
        yandex_folder_id = os.environ.get("yandex_folder_id")
        yandex_api_key = os.environ.get("yandex_api_key")

        # check if we have valid credentials
        if not ((yandex_api_key and yandex_folder_id) or yandex_iam_token):
            logger().warning(
                "no yandex speechkit credentials found in environment variables."
            )
            logger().warning(
                "for production use with yandex speechkit, obtain api credentials from https://cloud.yandex.com/"
            )
            logger().warning("set one of the following:")
            logger().warning("1. api key and folder id (legacy method):")
            logger().warning("  export yandex_api_key=your_api_key")
            logger().warning("  export yandex_folder_id=your_folder_id")
            logger().warning("2. iam token (preferred method):")
            logger().warning("  export yandex_iam_token=your_iam_token")
            logger().warning(
                "running in test mode with placeholder audio (silent files)."
            )

            # for testing purposes, use placeholder values
            yandex_api_key = "test_token"
            yandex_folder_id = "test_folder"

        # initialize tts with the appropriate credentials
        if yandex_iam_token:
            # use iam token (preferred method)
            tts = TextToSpeechYandex(
                iam_token=yandex_iam_token, folder_id=yandex_folder_id
            )
            logger().info("using yandex tts with iam token authentication")
        else:
            # use api key and folder id (legacy method)
            tts = TextToSpeechYandex(api_key=yandex_api_key, folder_id=yandex_folder_id)
            logger().info("using yandex tts with api key authentication")

        logger().info("using yandex tts for uzbek")
    elif selected_tts == "azure":
        azure_speech_key = os.environ.get("AZURE_SPEECH_KEY")
        azure_region = os.environ.get("AZURE_SPEECH_REGION")

        if not azure_speech_key or not azure_region:
            logger().warning(
                "missing azure tts credentials. using placeholder for testing."
            )
            logger().warning(
                "for production use with azure speech services, obtain api credentials from azure portal"
            )
            logger().warning("set them as environment variables or pass as arguments:")
            logger().warning(
                "  - azure_speech_key: azure speech services subscription key"
            )
            logger().warning(
                "  - azure_region: azure region (e.g., 'eastus', 'westeurope')"
            )
            logger().warning("for example:")
            logger().warning("  export azure_speech_key=your_subscription_key")
            logger().warning("  export azure_region=eastus")
            logger().warning(
                "running in test mode with placeholder audio (silent files)."
            )

        tts = TextToSpeechAzure(subscription_key=azure_speech_key, region=azure_region)
    elif selected_tts == "elevenlabs":
        elevenlabs_api_key = os.environ.get("elevenlabs_api_key")

        if not elevenlabs_api_key:
            logger().warning(
                "missing elevenlabs api key. using placeholder for testing."
            )
            logger().warning(
                "for production use with elevenlabs, obtain api key from https://elevenlabs.io/"
            )
            logger().warning("set it as environment variable:")
            logger().warning("  export elevenlabs_api_key=your_api_key")
            logger().warning(
                "running in test mode with placeholder audio (silent files)."
            )

        tts = TextToSpeechElevenLabs(api_key=elevenlabs_api_key)
        logger().info("using elevenlabs tts with multilingual support")
    else:
        raise ValueError(
            f"invalid tts value '{selected_tts}'. only 'yandex', 'azure', and 'elevenlabs' are supported."
        )

    return tts


def _get_selected_translator(
    provider: str, _device: str, rate_limiter=None, model_name: str | None = None
):
    """
    initialize and return the selected translation service.
    """
    if provider == "gemini":
        translation = GeminiTranslation(
            rate_limiter=rate_limiter, model_name=model_name
        )
    else:
        logger().warning(
            f"translation provider '{provider}' not implemented – falling back to gemini"
        )
        translation = GeminiTranslation(
            rate_limiter=rate_limiter, model_name=model_name
        )

    return translation


def _normalize_language_code(language_code: str) -> str:
    """
    normalize any language code to iso 639-3 format.

    args:
        language_code: language code in iso 639-1 (2-char) or iso 639-3 (3-char) format

    returns:
        normalized iso 639-3 language code
    """
    try:
        lang_obj = Lang(language_code)
        return lang_obj.pt3
    except Exception as e:
        logger().warning(f"failed to normalize language code '{language_code}': {e}")
        return language_code.lower()


def _are_languages_same(source_language: str, target_language: str) -> bool:
    """
    check if source and target languages are the same, handling different iso formats.

    args:
        source_language: source language code (iso 639-1 or iso 639-3)
        target_language: target language code (iso 639-1 or iso 639-3)

    returns:
        true if languages are the same, false otherwise
    """
    try:
        # normalize both languages to iso 639-3 for comparison
        source_lang_obj = Lang(source_language)
        target_lang_obj = Lang(target_language)

        # compare using iso 639-3 codes
        return source_lang_obj.pt3 == target_lang_obj.pt3
    except Exception as e:
        # if language parsing fails, fall back to direct string comparison
        logger().debug(
            f"language comparison fallback for '{source_language}' vs '{target_language}': {e}"
        )
        return source_language.lower() == target_language.lower()


def _get_optimal_device(device_arg: str) -> str:
    """
    determine the optimal device based on hardware acceleration detection.

    args:
        device_arg: the device argument from command line ("auto", "cpu", or "cuda")

    returns:
        the optimal device string ("cpu" or "cuda")
    """
    if device_arg == "auto":
        # automatically detect the best available hardware acceleration
        recommended_hwaccel = FFmpeg.get_recommended_hwaccel()

        # if cuda or nvenc is available, use cuda for ml models
        if recommended_hwaccel in [HWAccel.cuda, HWAccel.nvenc]:
            logger().info("auto-detected cuda acceleration - using cuda for ml models")
            return "cuda"
        else:
            logger().info("no cuda acceleration detected - using cpu for ml models")
            return "cpu"
    else:
        # user explicitly specified device
        return device_arg


def main():
    """main entry point for the dubbing application"""
    global current_dubber

    try:
        # register signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, handle_shutdown_signal)  # ctrl+c
        signal.signal(signal.SIGTERM, handle_shutdown_signal)  # termination request

        args = CommandLine.read_parameters()
        trans_model_name = None

        # if unified run configuration is provided, prefer its log level
        if getattr(args, "run_config", None):
            cfg = args.run_config
            _init_logging(cfg.output.log_level, cfg.output.log_format)
        else:
            _init_logging(args.log_level)

        # apply selected values from unified run configuration (non-breaking minimal integration)
        if getattr(args, "run_config", None):
            cfg = args.run_config

            # map essential parameters; cli values still take precedence if explicitly provided
            args.input_file = args.input_file or str(cfg.input.source_path)
            args.output_directory = args.output_directory or str(cfg.output.output_dir)
            args.target_language = args.target_language or cfg.translation.target_lang

            # segmentation overrides if cli left defaults
            if args.min_silence_duration == 0.5:  # cli default
                args.min_silence_duration = cfg.input.silence.padding_seconds
            if args.silence_threshold == -35:  # cli default
                args.silence_threshold = (
                    cfg.input.silence.threshold * -100
                    if cfg.input.silence.method == "energy"
                    else args.silence_threshold
                )

            # cpu threads / workers
            if args.cpu_threads == 0:
                args.cpu_threads = cfg.performance.max_workers

            # device selection hint
            if args.device == "auto":
                args.device = "cuda" if cfg.performance.use_gpu else "cpu"

            # tts provider override if cli default (yandex)
            if args.tts == "yandex":
                args.tts = cfg.models.tts_provider

            # stt provider override if cli default (faster-whisper)
            if args.stt == "faster-whisper":
                args.stt = cfg.models.stt_provider

            trans_model_name = cfg.translation.analyzer.model

            # override video encoder
            if cfg.output.video_encoder:
                VideoProcessing._GPU_ENCODER = cfg.output.video_encoder
        else:
            trans_model_name = None

        # ensure translation provider attribute exists for downstream selection
        if not hasattr(args, "translation_provider"):
            args.translation_provider = "gemini"

        if getattr(args, "run_config", None):
            args.translation_provider = cfg.translation.analyzer.provider

        # handle list_runs command
        if args.list_runs:
            run_manager = RunManager(args.output_directory)
            runs = run_manager.list_runs()

            if not runs:
                print("no runs found.")
                return

            print(f"found {len(runs)} runs:")
            print("-" * 80)
            for run in runs:
                print(f"run id: {run.run_id}")
                print(f"created: {run.created_at}")
                print(f"status: {run.status}")
                print(f"input: {run.input_file}")
                print(f"languages: {run.source_language} -> {run.target_language}")
                print(f"files: {len(run.files_created)} created")
                print("-" * 80)
            return

        check_is_a_video(args.input_file)

        hugging_face_token = get_token(args.hugging_face_token)

        if not FFmpeg.is_ffmpeg_installed():
            msg = "you need to have ffmpeg (which includes ffprobe) installed."
            log_error_and_exit(msg, ExitCode.NO_FFMPEG)

        # log available hardware acceleration methods
        available_hwaccel = FFmpeg.get_available_hwaccel_methods()
        recommended_hwaccel = FFmpeg.get_recommended_hwaccel()
        logger().info(
            f"available hardware acceleration methods: {[method.value for method, available in available_hwaccel.items() if available]}"
        )
        logger().info(
            f"selected hardware acceleration method: {recommended_hwaccel.value}"
        )

        # determine optimal device for ml models based on hardware acceleration
        optimal_device = _get_optimal_device(args.device)
        logger().info(
            f"using device '{optimal_device}' for ml models (requested: '{args.device}')"
        )

        # check if the system has the imageio_ffmpeg_exe environment variable set
        if "imageio_ffmpeg_exe" not in os.environ:
            logger().warning(
                "imageio_ffmpeg_exe environment variable not set. gpu acceleration will not be used."
            )
        else:
            logger().info(
                f"using system ffmpeg for moviepy operations: {os.environ['imageio_ffmpeg_exe']}"
            )

            # check if the selected encoder is actually a hardware encoder
            if VideoProcessing._GPU_ENCODER == "libx264":
                logger().warning(
                    "using cpu-based encoder (libx264). video processing may be slower."
                )
            else:
                logger().info(
                    f"using hardware encoder: {VideoProcessing._GPU_ENCODER.lower()}"
                )

        # log performance expectations based on hardware acceleration
        if recommended_hwaccel != HWAccel.none and "imageio_ffmpeg_exe" in os.environ:
            if VideoProcessing._GPU_ENCODER != "libx264":
                logger().info(
                    "gpu acceleration enabled for all ffmpeg operations. expect significantly improved performance."
                )
        else:
            logger().warning(
                "running without full gpu acceleration. processing will be slower."
            )

        tts = _get_selected_tts(
            args.tts,
            optimal_device,
        )

        if sys.platform == "darwin":
            os.environ["tokenizers_parallelism"] = "false"

        # initialize speech-to-text based on selected provider
        if args.stt == "assemblyai":
            assemblyai_api_key = os.environ.get("ASSEMBLYAI_API_KEY")
            if not assemblyai_api_key:
                logger().warning(
                    "no assemblyai api key found in environment variables."
                )
                logger().warning(
                    "for production use with assemblyai, obtain api key from https://www.assemblyai.com/"
                )
                logger().warning("set it as environment variable:")
                logger().warning("  export ASSEMBLYAI_API_KEY=your_api_key")
                logger().warning(
                    "running in test mode with placeholder transcriptions."
                )

            stt = SpeechToTextAssemblyAI(
                model_name="best",
                device=optimal_device,
                cpu_threads=args.cpu_threads,
                api_key=assemblyai_api_key,
                speaker_labels=not args.skip_diarization,  # use speaker labels if diarization is enabled
                language_detection=not args.source_language,  # auto-detect if no source language specified
            )
            stt_text = "assemblyai"
            if not args.skip_diarization:
                stt_text += " (with speaker labels)"
        else:
            # default to faster-whisper
            stt = SpeechToTextFasterWhisper(
                model_name=args.whisper_model,
                device=optimal_device,
                cpu_threads=args.cpu_threads,
                vad=args.vad,
                initial_prompt=None,
            )
            stt_text = "faster-whisper"
            if args.vad:
                stt_text += " (with vad filter)"

        stt.load_model()
        source_language = args.source_language
        if not source_language:
            source_language = stt.detect_language(args.input_file)
            logger().info(f"detected language '{source_language}'")

        # normalize language codes to iso 639-3 format
        source_language = _normalize_language_code(source_language)
        target_language = _normalize_language_code(args.target_language)

        logger().info(
            f"using source language: '{source_language}' -> target language: '{target_language}'"
        )

        # check if source and target languages are the same
        if _are_languages_same(source_language, target_language):
            msg = f"source language '{source_language}' and target language '{target_language}' are the same. no dubbing needed."
            log_error_and_exit(msg, ExitCode.SAME_LANGUAGE)

        # use gemini translator
        shared_rate_limiter = None
        if getattr(args, "run_config", None):
            if cfg.performance.rate_limit_per_sec > 0:
                from src.utils.rate_limiter import RateLimiter

                shared_rate_limiter = RateLimiter(cfg.performance.rate_limit_per_sec)

        translation = _get_selected_translator(
            args.translation_provider,
            optimal_device,
            shared_rate_limiter,
            trans_model_name,
        )

        check_languages(
            source_language,
            target_language,
            tts,
            translation,
            stt,
            args.target_language_region,
        )

        if not os.path.exists(args.output_directory):
            os.makedirs(args.output_directory)

        dubber = Dubber(
            input_file=args.input_file,
            output_directory=args.output_directory,
            source_language=source_language,
            target_language=target_language,
            target_language_region=args.target_language_region,
            hugging_face_token=hugging_face_token,
            tts=tts,
            translation=translation,
            stt=stt,
            device=optimal_device,
            cpu_threads=args.cpu_threads,
            clean_intermediate_files=args.clean_intermediate_files,
            original_subtitles=args.original_subtitles,
            dubbed_subtitles=args.dubbed_subtitles,
            use_enhanced_segmentation=args.enhanced_segmentation,
            min_silence_duration=args.min_silence_duration,
            silence_threshold=args.silence_threshold,
            max_segment_duration=args.max_segment_duration,
            skip_diarization=args.skip_diarization,
            run_config=args.run_config,
        )

        # set the global dubber instance for the signal handler to use
        current_dubber = dubber

        # display the run uuid for tracking
        logger().info(f"dubbing run id: {dubber.get_run_id()}")
        logger().info(f"run output directory: {dubber.get_run_output_directory()}")

        logger().info(
            f"processing '{args.input_file}' file with stt '{stt_text}', tts '{args.tts}' and device '{optimal_device}'"
        )
        logger().info(f"using {args.tts} tts for {target_language}")

        try:
            if args.update:
                dubber.update()
            else:
                dubber.dub()
        except KeyboardInterrupt:
            # this should be caught by our signal handler, but just in case
            logger().info("interrupted by user. exiting gracefully...")
            sys.exit(0)
        except Exception as e:
            logger().error(f"error during dubbing process: {e}")
            sys.exit(1)
        finally:
            # ensure cleanup happens
            try:
                dubber.cleanup()
            except Exception as e:
                logger().error(f"error during cleanup: {e}")
            # reset the global dubber instance
            current_dubber = None
            # force exit to clean up any hanging threads
            os._exit(0)

    except Exception as e:
        logger().error(f"fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
