🎨🎨🎨 entering creative phase: architecture

# component description

the **unified run configuration** defines all adjustable parameters required by the aizen platform processing pipeline. it replaces scattered configs and hard-coded constants with a single json file that is validated by pydantic models at startup.

# requirements & constraints

-   must cover five logical sections:
    1. input processing configuration
    2. translation pipeline configuration
    3. performance and resource management
    4. model configurations
    5. output and logging configuration
-   file format: json for maximum ecosystem compatibility
-   schema defined with `pydantic>=2.7`; strict validation enabled
-   sensible defaults for optional fields
-   backwards compatible: legacy cli flags/env vars may override loaded values
-   must support environment-specific overrides (e.g., staging vs production)

# design options

## option 1 – flat top-level keys

a single `run_config.json` with all keys at the top level. each pipeline stage accesses its keys directly.

### pros

-   simplest structure to read/write
-   zero nesting overhead

### cons

-   no logical grouping → difficult to reason about
-   higher risk of key collisions
-   validation logic becomes large and error-prone

---

## option 2 – nested hierarchical models (one model per section)

a pydantic `RunConfig` root model encapsulates nested models for each section (`InputProcessingConfig`, `TranslationConfig`, etc.). modules receive only their relevant sub-models.

### pros

-   clear separation of concerns
-   strong typing per section
-   easier unit testing and documentation
-   future extensions localized to a single sub-model

### cons

-   slight overhead when accessing deeply nested values
-   requires more verbose json structure

---

## option 3 – hierarchical models + environment overlay

same as option 2 but introduces an optional `environments` map (e.g., `dev`, `prod`) where each entry overrides the base config. loader merges base + selected env at runtime.

### pros

-   supports multi-environment deployments out of the box
-   keeps base defaults while allowing overrides

### cons

-   adds merge logic complexity
-   if misused, may hide unintended overrides

# option analysis

| criteria                    | weight | option 1 | option 2 | option 3 |
| --------------------------- | ------ | -------- | -------- | -------- |
| clarity & maintainability   | 0.35   | 0.2      | **0.9**  | 0.8      |
| type safety                 | 0.25   | 0.3      | **0.9**  | **0.9**  |
| extensibility               | 0.2    | 0.4      | **0.8**  | **0.9**  |
| implementation complexity ↓ | 0.1    | **0.9**  | 0.7      | 0.6      |
| env support                 | 0.1    | 0.4      | 0.5      | **0.9**  |
| **weighted score**          | 1.0    | 0.39     | **0.82** | 0.81     |

# recommended approach

option 2 wins by a small margin due to its high clarity, safety, and extensibility, while keeping complexity reasonable. environment overlays can be added later if needed (progressive enhancement principle).

# implementation guidelines

1. **schema definition** (`src/config.py`)

    ```python
    from pathlib import path
    from typing import list, literal, optional
    from pydantic import basemodel, field, fieldvalidationinfo

    class silencedetectionconfig(basemodel):
        method: literal["energy", "webrtc"] = "energy"
        threshold: float = 0.02  # relative energy level
        padding_seconds: float = 0.25

    class audioprocessingconfig(basemodel):
        model: str = "htdemucs"
        stems: list[str] = ["vocals", "accompaniment"]
        output_format: str = "wav"
        segment_duration: float = 30.0
        extra_args: list[str] = []

    class inputprocessingconfig(basemodel):
        source_path: path
        source_format: str = "mp4"
        temp_dir: path = path("./tmp")
        silence: silencedetectionconfig = silencedetectionconfig()
        separation: audioprocessingconfig = audioprocessingconfig()

    class translationmodellayer(basemodel):
        provider: literal["google", "gemini", "yandex"]
        model: str
        temperature: float = 0.2
        top_p: float = 0.95

    class translationconfig(basemodel):
        source_lang: str = "en"
        target_lang: str = "es"
        analyzer: translationmodellayer
        segment_processor: translationmodellayer

    class performanceconfig(basemodel):
        max_workers: int = 4
        rate_limit_per_sec: int = 2
        use_gpu: bool = true
        max_memory_gb: optional[float] = none

    class modelconfig(basemodel):
        transcription_chunk_size: float = 30.0
        timestamp_window: float = 0.5
        tts_voice: str = "eleven_multilingual_v2"
        temperature: float = 0.7

    class outputconfig(basemodel):
        output_dir: path = path("./outputs")
        naming_pattern: str = "{basename}_{lang}.{ext}"
        log_level: literal["debug", "info", "warning", "error"] = "info"
        log_format: str = "%(asctime)s | %(levelname)s | %(message)s"

    class runconfig(basemodel):
        input: inputprocessingconfig
        translation: translationconfig
        performance: performanceconfig = performanceconfig()
        models: modelconfig = modelconfig()
        output: outputconfig = outputconfig()

        @classmethod
        def load(cls, path: str) -> "runconfig":
            import json, pathlib
            data = json.loads(pathlib.path(path).read_text())
            return cls.model_validate(data)
    ```

2. **loader usage**

    ```python
    from config import runconfig
    cfg = runconfig.load(args.config)
    dubber = dubber(cfg)
    ```

3. **module injection pattern**

    - each pipeline component receives only its subsection:
        ```python
        speech_to_text = fasterwhisper(config=models).
        separator = demucs(config=cfg.input.separation)
        ```

4. **default config file**
   create `run_config.example.json` mirroring the schema with safe defaults.

5. **cli integration** in `command_line.py`

    - add `--config path` option, defaulting to `run_config.json` if present else example file.

6. **logger integration**

    - call `logging.basicconfig(level=cfg.output.log_level.upper(), format=cfg.output.log_format)` in `utils/logger.py`.

7. **validation & tests**
    - unit tests ensure `runconfig.load` fails gracefully on missing required fields or wrong types.
    - sample pipeline e2e test loads example config and processes a short clip.

# verification checkpoint

✓ requirement coverage: all five sections included
✓ sensible defaults provided
✓ pydantic schema with strict validation
✓ loader returns structured object
✓ injection pattern supports backward compatibility

🎨🎨🎨 exiting creative phase
