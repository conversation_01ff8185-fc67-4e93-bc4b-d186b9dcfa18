from __future__ import annotations

"""simple token-bucket style rate limiter for synchronously throttling api calls."""

import time
import threading


class RateLimiter:
    """limits calls to *rate* per second.

    usage
    -----
    >>> limiter = RateLimiter(rate_per_sec=5)
    >>> limiter.wait()  # blocks so that max 5 calls/sec overall
    """

    def __init__(self, rate_per_sec: float) -> None:
        if rate_per_sec <= 0:
            # treat non-positive as unlimited
            self._interval = 0.0
        else:
            self._interval = 1.0 / float(rate_per_sec)
        self._lock = threading.Lock()
        self._last_call = 0.0

    def wait(self) -> None:
        """block until it is safe to perform the next call."""
        if self._interval == 0.0:
            return
        with self._lock:
            now = time.monotonic()
            elapsed = now - self._last_call
            to_wait = self._interval - elapsed
            if to_wait > 0:
                time.sleep(to_wait)
            self._last_call = time.monotonic()


__all__ = ["RateLimiter"]
