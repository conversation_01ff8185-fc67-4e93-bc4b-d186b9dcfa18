# active context

> this file contains the active context for the current task.

## recently completed

-   **task: unified run configuration**
    -   **status:** archived
    -   **archive:** [archive-unified-run-configuration.md](memory-bank/archive/archive-unified-run-configuration.md)
-   **task: file path safety enhancement**
    -   **status:** archived
    -   **archive:** [archive-filepath-safety.md](memory-bank/archive/archive-filepath-safety.md)

## current focus

awaiting new task assignment.

---

_this file is dynamically updated._

## van mode: platform detection

-   **operating system**: windows 10 (version 10.0 26100)
-   **shell**: powershell 7
-   **path separator**: \
-   **command adaptations**: powershell-compatible commands will be used.

## platform

-   windows 10 (version 10.0 26100)
-   python 3.11.11 (strict requirement)
-   cuda support for gpu acceleration
-   ffmpeg required for video/audio processing

## project summary

aizen platform is an advanced ai-powered video dubbing system that automatically translates and synchronizes audio dialogue from videos into different languages. the system uses a complex pipeline involving audio separation, speech recognition, translation, and text-to-speech synthesis.

## validation status

✓ memory bank structure verified
✓ basic file verification completed
✓ platform detection completed

## important references

-   run configuration: run_config.json
-   configuration schema: src/config.py

## related files

_no related files defined_
