# aizen platform - ai dubbing system

<div align="center">

![python](https://img.shields.io/badge/python-3.11.11-blue.svg)
![license](https://img.shields.io/badge/license-none-red.svg)
![status](https://img.shields.io/badge/status-development-yellow.svg)

**an advanced ai-powered video dubbing system that automatically translates and synchronizes audio dialogue into different languages**

[features](#-features) • [quick start](#-quick-start) • [installation](#-installation) • [usage](#-usage) • [configuration](#-configuration)

</div>

---

## 🎯 features

-   **🎬 video processing**: supports mp4 video files with automatic audio/video separation
-   **🎵 vocal isolation**: uses demucs for high-quality vocal separation from background music
-   **👥 speaker diarization**: pyannote-based speaker identification and segmentation
-   **🗣️ speech-to-text**: faster-whisper and assemblyai integration with multiple model options
-   **🌍 translation**: google gemini-powered translation to any target language
-   **🎤 text-to-speech**: multiple tts providers (yandex, azure, elevenlabs)
-   **⚡ gpu acceleration**: cuda support for faster processing
-   **📝 subtitles**: automatic generation of srt and vtt subtitle files

-   **🔄 incremental updates**: smart re-processing with metadata caching
-   **🆔 run organization**: uuid-based file organization for isolated dubbing sessions

---

## 🚀 quick start

```bash
# 1. clone and setup environment
git clone <repository-url>
cd aizen_platform/server
uv venv --python=3.11.11
uv sync

# 2. configure environment variables
cp .env.example .env
# edit .env with your api keys

# 3. run basic dubbing (english → russian)
python -m src.core.main --input_file path/to/video.mp4 --target_language ru
```

**output**:

-   run id: `2292263e-3e65-4d45-acdd-6bd1c7faaf08` (displayed during processing)
-   dubbed video saved in: `data/outputs/2292263e-3e65-4d45-acdd-6bd1c7faaf08/`

---

## 📦 installation

### prerequisites

| requirement    | version | purpose                           |
| -------------- | ------- | --------------------------------- |
| python         | 3.11.11 | core runtime (strict requirement) |
| ffmpeg         | ≥ 5.0   | video/audio processing            |
| nvidia drivers | ≥ 550   | gpu acceleration (optional)       |
| git lfs        | latest  | large model files (optional)      |

### system dependencies

**ubuntu/debian:**

```bash
sudo apt update
sudo apt install build-essential libsndfile1 ffmpeg
```

**windows:**

```powershell
# install ffmpeg via chocolatey
choco install ffmpeg
```

### python environment

```bash
# create virtual environment with exact python version
uv venv --python=3.11.11
source .venv/bin/activate  # linux/mac
# or .venv\Scripts\activate  # windows

# install dependencies
uv sync
```

---

## 🎮 usage

### basic usage

```bash
# minimal command (uzbek)
python -m src.core.main --input_file video.mp4 --target_language uz

# with gpu acceleration (russian)
python -m src.core.main --input_file video.mp4 --target_language ru --device cuda

# high quality with subtitles (english)
python -m src.core.main --input_file video.mp4 --target_language en --whisper_model large-v3 --dubbed_subtitles --original_subtitles

# using assemblyai for speech-to-text (uzbek)
python -m src.core.main --input_file video.mp4 --target_language uz --stt assemblyai
```

### advanced usage

```bash
# custom tts provider with enhanced segmentation (uzbek)
python -m src.core.main --input_file video.mp4 --target_language uz --tts azure --enhanced_segmentation --min_silence_duration 0.3 --max_segment_duration 20

# assemblyai with speaker diarization and azure tts (russian)
python -m src.core.main --input_file video.mp4 --target_language ru --stt assemblyai --tts azure

# incremental update (modify existing dubbing) (russian)
python -m src.core.main --input_file video.mp4 --target_language ru --update --output_directory data/outputs/existing_run/

# list all dubbing runs
python -m src.core.main --list_runs
```

### supported languages

the system supports both 2-letter iso-639-1 and 3-letter iso-639-3 language codes for maximum compatibility. you can use either format:

**language code formats:**

-   **2-letter codes (recommended):** `uz`, `ru`, `en`
-   **3-letter codes (legacy support):** `uzb`, `rus`, `eng`

the platform currently supports three languages across all tts providers:

**supported languages (all providers):**

-   `uz`/`uzb` (uzbek)
-   `ru`/`rus` (russian)
-   `en`/`eng` (english)

**note:** both language code formats are fully supported. the system automatically handles conversion between formats internally.

### run management

the system automatically generates a unique uuid for each dubbing session and provides tools to manage runs:

```bash
# list all runs with status and metadata
python -m src.core.main --list_runs

# each run displays:
# - run id: 2292263e-3e65-4d45-acdd-6bd1c7faaf08
# - created: 2025-01-15t14:30:22
# - status: completed
# - input: video.mp4
# - languages: eng -> spa
# - files: 7 created
```

**run tracking features:**

-   **automatic uuid generation**: each run gets a unique identifier
-   **status tracking**: running, completed, failed
-   **metadata storage**: input file, languages, creation time
-   **file inventory**: automatic tracking of all generated files
-   **easy retrieval**: reference runs by uuid for updates or analysis

---

## ⚙️ configuration

### environment variables

create a `.env` file in the project root:

```env
# yandex speechkit
YANDEX_OAUTH_TOKEN=your_oauth_token
YANDEX_FOLDER_ID=your_folder_id
YANDEX_IAM_TOKEN=your_iam_token  # preferred method

# azure cognitive services
AZURE_SPEECH_KEY=your_speech_key
AZURE_SPEECH_REGION=your_region

# elevenlabs
ELEVENLABS_API_KEY=your_api_key

# assemblyai
ASSEMBLYAI_API_KEY=your_api_key

# google services
GOOGLE_API_KEY=your_gemini_api_key

# hugging face
HF_TOKEN=your_hf_token
```

### cli reference

| parameter                    | default           | description                                                                        |
| ---------------------------- | ----------------- | ---------------------------------------------------------------------------------- |
| `--input_file`               | required          | path to input mp4 video                                                            |
| `--target_language`          | required          | target language code (2-letter: `uz`, `ru`, `en` or 3-letter: `uzb`, `rus`, `eng`) |
| `--source_language`          | auto-detect       | source language code (2-letter: `uz`, `ru`, `en` or 3-letter: `uzb`, `rus`, `eng`) |
| `--output_directory`         | `data/outputs/`   | output directory path                                                              |
| `--tts`                      | `yandex`          | tts provider (yandex/azure/elevenlabs)                                             |
| `--stt`                      | `faster-whisper`  | speech-to-text provider (faster-whisper/assemblyai)                                |
| `--whisper_model`            | `distil-large-v3` | whisper model size                                                                 |
| `--device`                   | `cpu`             | processing device (cpu/cuda)                                                       |
| `--enhanced_segmentation`    | `true`            | natural pause detection                                                            |
| `--skip_diarization`         | `false`           | skip speaker identification                                                        |
| `--clean-intermediate-files` | `false`           | cleanup temp files                                                                 |
| `--dubbed_subtitles`         | `false`           | generate dubbed subtitles                                                          |
| `--original_subtitles`       | `false`           | generate original subtitles                                                        |
| `--update`                   | `false`           | incremental update mode                                                            |
| `--list_runs`                | `false`           | list all available dubbing runs                                                    |
| `--run_id`                   | auto-generate     | specify or reference a specific run uuid                                           |

<details>
<summary>view all cli options</summary>

```bash
python -m src.core.main --help
```

see `src/core/command_line.py` for complete parameter documentation.

</details>

### additional configuration fields

| section                                  | key                                                           | description                                                                    | default          |
| ---------------------------------------- | ------------------------------------------------------------- | ------------------------------------------------------------------------------ | ---------------- |
| input.separation                         | model                                                         | demucs model name to use for source separation                                 | `htdemucs_ft`    |
| input.separation                         | segment_duration                                              | chunk length in seconds for demucs `--segment`                                 | `30`             |
| translation.analyzer / segment_processor | model                                                         | specific model identifier for provider (e.g. `gemini-2.5-flash-preview-05-20`) | provider default |
| models.tts_provider                      | provider used for tts (`yandex`, `azure`, `elevenlabs`)       | `yandex`                                                                       |
| models.tts_voice                         | preferred voice id / name applied to all speakers (optional)  | _none_                                                                         |
| performance.rate_limit_per_sec           | global api call throttle (0 = unlimited)                      | `2`                                                                            |
| output.video_encoder                     | ffmpeg encoder to use (`h264`, `libx264`, `h264_nvenc`, etc.) | `libx264`                                                                      |

---

## 🏗️ architecture

### processing pipeline

```mermaid
graph TD
    A[input video] --> B[audio/video separation]
    B --> C[vocal isolation - demucs]
    C --> D[speaker diarization - pyannote]
    D --> E[speech segmentation]
    E --> F[speech-to-text - whisper]
    F --> G[translation - gemini]
    G --> H[voice assignment]
    H --> I[text-to-speech]
    I --> J[audio merging]
    J --> K[subtitle generation]
    K --> L[final video assembly]
    L --> M[cloud backup - optional]
```

### project structure

```
aizen_platform/server/
├── src/
│   ├── core/              # main orchestration
│   │   ├── main.py        # entry point
│   │   ├── dubbing.py     # dubbing orchestrator
│   │   └── command_line.py # cli parser
│   ├── audio_processing/  # audio manipulation
│   │   ├── demucs/        # vocal isolation
│   │   ├── whisper/       # speech-to-text
│   │   └── tts/           # text-to-speech providers
│   ├── video_processing/  # video operations
│   │   ├── ffmpeg.py      # ffmpeg wrapper
│   │   └── subtitles.py   # subtitle generation
│   ├── translation/       # translation engines
│   ├── api/              # cloud service wrappers
│   └── utils/            # shared utilities
│       └── run_manager.py # uuid-based run management
├── data/
│   ├── inputs/           # input videos (optional)
│   └── outputs/          # processing results
├── scripts/              # utility scripts
└── pyproject.toml        # dependencies
```

### output structure

each dubbing run creates a uuid-based directory for complete isolation:

```
data/outputs/
├── runs_registry.json                    # run tracking registry
├── 2292263e-3e65-4d45-acdd-6bd1c7faaf08/ # run 1 (uuid directory)
│   ├── dubbed_audio_spa.mp3              # final dubbed audio
│   ├── dubbed_vocals.mp3                 # vocals track
│   ├── spa.srt                          # dubbed subtitles
│   ├── eng.srt                          # original subtitles
│   ├── utterance_metadata.json          # processing metadata
│   ├── video_audio.wav                  # extracted audio
│   ├── video_video.mp4                  # video without audio
│   └── transcription.txt                # debug transcription
├── a1b2c3d4-e5f6-4789-90ab-123456789012/ # run 2 (different uuid)
│   ├── dubbed_audio_rus.mp3
│   ├── dubbed_vocals.mp3
│   ├── rus.srt
│   ├── eng.srt
│   └── utterance_metadata.json
└── ...                                  # additional runs
```

**benefits of uuid organization:**

-   **complete isolation**: each run has its own directory
-   **easy identification**: runs identified by unique uuid
-   **no file conflicts**: multiple runs can process simultaneously
-   **run tracking**: comprehensive registry with metadata and status
-   **easy cleanup**: remove specific runs without affecting others

---

## 🛠️ development

### setup development environment

```bash
# clone repository
git clone <repository-url>
cd aizen_platform/server

# setup python environment
uv venv --python=3.11.11
uv sync --dev

# install pre-commit hooks
pre-commit install
```

### code quality

```bash
# format code
black src/
ruff check src/ --fix

# type checking
mypy src/

# pre-commit checks
pre-commit run --all-files
```

### adding new tts providers

1. create new provider class in `src/audio_processing/text_to_speech_<provider>.py`
2. implement required interface methods
3. add provider to `src/core/main.py` tts selection
4. update cli options and documentation

---

## 🤝 contributing

we welcome contributions! please follow these guidelines:

### commit conventions

use conventional commits for clear history:

```bash
feat: add new tts provider support
fix: resolve audio sync issues
docs: update installation guide
refactor: improve error handling
test: add integration tests
```

### code style

-   follow pep 8 guidelines
-   use lowercase for comments and logging
-   line length ≤ 100 characters
-   add type hints for new functions
-   write docstrings for public methods

### pull request process

1. fork the repository
2. create feature branch: `git checkout -b feature/amazing-feature`
3. commit changes: `git commit -m 'feat: add amazing feature'`
4. push to branch: `git push origin feature/amazing-feature`
5. open pull request with detailed description

---

## 📋 roadmap

### upcoming features

-   [x] **uuid organization**: unique run identification and file isolation ✅
-   [ ] **web interface**: streamlit-based gui for easier usage
-   [ ] **batch processing**: multiple video processing support
-   [ ] **voice cloning**: custom voice training capabilities
-   [ ] **real-time dubbing**: live video stream processing
-   [ ] **quality metrics**: automated dubbing quality assessment

### technical improvements

-   [ ] **ci/cd**: github actions workflow
-   [ ] **docker**: containerized deployment
-   [ ] **api**: rest api for programmatic access
-   [ ] **monitoring**: performance and error tracking

---

## 📄 license

this project currently has no license. please add an appropriate open source license before public distribution.

---

## 🆘 support

### troubleshooting

**common issues:**

1. **ffmpeg not found**: ensure ffmpeg is installed and in system path
2. **cuda errors**: verify nvidia drivers and cuda toolkit installation
3. **api key errors**: check environment variable configuration
4. **memory issues**: reduce batch size or use cpu processing

**getting help:**

-   check existing issues in the repository
-   review logs in `data/outputs/<run>/` for detailed error information
-   ensure all prerequisites are properly installed

### performance optimization

**for faster processing:**

-   use gpu acceleration with `--device cuda`
-   choose appropriate whisper model size
-   enable intermediate file cleanup
-   use enhanced segmentation for better accuracy

**for better quality:**

-   use `large-v3` whisper model
-   enable speaker diarization
-   adjust segmentation parameters
-   choose premium tts providers

---

## unified run configuration

the platform now supports a single json configuration file to control the entire processing pipeline.

example:

```bash
python -m src.core.main --config run_config.json
```

see `run_config.json` for all available fields. modify values such as input source, separation model, translation & tts providers, performance limits, and logging format without touching code.

---

<div align="center">

**made with ❤️ by the aizen platform team**

</div>
