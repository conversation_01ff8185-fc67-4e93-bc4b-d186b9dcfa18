import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from src.utils.logger import logger


@dataclass
class RunInfo:
    """information about a dubbing run."""

    run_id: str
    created_at: str
    input_file: str
    source_language: str
    target_language: str
    status: str  # "running", "completed", "failed"
    output_directory: str
    files_created: List[str]
    metadata: Dict[str, Any]


class RunManager:
    """manages dubbing runs and provides tracking/retrieval functionality."""

    def __init__(self, base_output_directory: str):
        self.base_output_directory = base_output_directory
        self.runs_registry_file = os.path.join(
            base_output_directory, "runs_registry.json"
        )

    def register_run(self, run_info: RunInfo) -> None:
        """registers a new run in the registry."""
        registry = self._load_registry()
        registry[run_info.run_id] = asdict(run_info)
        self._save_registry(registry)
        logger().info(f"registered run {run_info.run_id} in registry")

    def update_run_status(
        self, run_id: str, status: str, files_created: Optional[List[str]] = None
    ) -> None:
        """updates the status and files for a run."""
        registry = self._load_registry()
        if run_id in registry:
            registry[run_id]["status"] = status
            if files_created:
                registry[run_id]["files_created"] = files_created
            self._save_registry(registry)
            logger().info(f"updated run {run_id} status to {status}")
        else:
            logger().warning(f"run {run_id} not found in registry")

    def get_run_info(self, run_id: str) -> Optional[RunInfo]:
        """gets information about a specific run."""
        registry = self._load_registry()
        if run_id in registry:
            return RunInfo(**registry[run_id])
        return None

    def list_runs(self, status_filter: Optional[str] = None) -> List[RunInfo]:
        """lists all runs, optionally filtered by status."""
        registry = self._load_registry()
        runs = []
        for run_data in registry.values():
            if status_filter is None or run_data.get("status") == status_filter:
                runs.append(RunInfo(**run_data))
        return sorted(runs, key=lambda x: x.created_at, reverse=True)

    def get_run_files(self, run_id: str) -> List[str]:
        """gets all files created by a specific run."""
        run_info = self.get_run_info(run_id)
        if run_info:
            return run_info.files_created
        return []

    def get_run_directory(self, run_id: str) -> Optional[str]:
        """gets the output directory for a specific run."""
        run_info = self.get_run_info(run_id)
        if run_info:
            return run_info.output_directory
        return None

    def discover_runs(self) -> List[str]:
        """discovers all run directories by scanning the base output directory."""
        if not os.path.exists(self.base_output_directory):
            return []

        run_ids = []
        for item in os.listdir(self.base_output_directory):
            item_path = os.path.join(self.base_output_directory, item)
            if os.path.isdir(item_path) and self._is_valid_uuid(item):
                run_ids.append(item)
        return run_ids

    def scan_run_files(self, run_id: str) -> List[str]:
        """scans a run directory to discover all files created."""
        run_dir = os.path.join(self.base_output_directory, run_id)
        if not os.path.exists(run_dir):
            return []

        files = []
        for root, dirs, filenames in os.walk(run_dir):
            for filename in filenames:
                file_path = os.path.join(root, filename)
                # store relative path from run directory
                rel_path = os.path.relpath(file_path, run_dir)
                files.append(rel_path)
        return files

    def cleanup_run(self, run_id: str, remove_files: bool = False) -> bool:
        """cleans up a run from the registry and optionally removes files."""
        registry = self._load_registry()
        if run_id not in registry:
            logger().warning(f"run {run_id} not found in registry")
            return False

        if remove_files:
            run_dir = os.path.join(self.base_output_directory, run_id)
            if os.path.exists(run_dir):
                import shutil

                shutil.rmtree(run_dir)
                logger().info(f"removed run directory {run_dir}")

        del registry[run_id]
        self._save_registry(registry)
        logger().info(f"removed run {run_id} from registry")
        return True

    def _load_registry(self) -> Dict[str, Any]:
        """loads the runs registry from file."""
        if not os.path.exists(self.runs_registry_file):
            return {}

        try:
            with open(self.runs_registry_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger().warning(f"error loading runs registry: {e}")
            return {}

    def _save_registry(self, registry: Dict[str, Any]) -> None:
        """saves the runs registry to file."""
        try:
            os.makedirs(os.path.dirname(self.runs_registry_file), exist_ok=True)
            with open(self.runs_registry_file, "w", encoding="utf-8") as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger().error(f"error saving runs registry: {e}")

    def _is_valid_uuid(self, uuid_string: str) -> bool:
        """checks if a string is a valid uuid4 format."""
        import re

        uuid_pattern = re.compile(
            r"^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$",
            re.IGNORECASE,
        )
        return bool(uuid_pattern.match(uuid_string))

    def get_run_summary(self, run_id: str) -> Optional[Dict[str, Any]]:
        """gets a summary of a run including file counts and sizes."""
        run_info = self.get_run_info(run_id)
        if not run_info:
            return None

        run_dir = os.path.join(self.base_output_directory, run_id)
        if not os.path.exists(run_dir):
            return None

        files = self.scan_run_files(run_id)
        total_size = 0
        file_types = {}

        for file_path in files:
            full_path = os.path.join(run_dir, file_path)
            if os.path.exists(full_path):
                size = os.path.getsize(full_path)
                total_size += size

                ext = os.path.splitext(file_path)[1].lower()
                if ext in file_types:
                    file_types[ext] += 1
                else:
                    file_types[ext] = 1

        return {
            "run_info": asdict(run_info),
            "file_count": len(files),
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "file_types": file_types,
            "files": files,
        }
