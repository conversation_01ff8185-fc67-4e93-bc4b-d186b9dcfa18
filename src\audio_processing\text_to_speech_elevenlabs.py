# this file was rewritten by inspire

import os
from typing import List, Optional, Iterator

import numpy as np
import scipy.io.wavfile
from pydub import AudioSegment
from tenacity import retry, stop_after_attempt, wait_exponential

try:
    from elevenlabs.client import ElevenLabs

    from elevenlabs.types import VoiceSettings
    from elevenlabs.core import ApiError

    from elevenlabs import save as elevenlabs_save

    ELEVENLABS_AVAILABLE = True
except ImportError:
    ELEVENLABS_AVAILABLE = False

    class ElevenLabs:
        pass

    class VoiceSettings:
        pass

    class ApiError(Exception):
        pass

    def elevenlabs_save(*args, **kwargs):
        pass


from src.utils.logger import logger
from src.audio_processing.text_to_speech import TextToSpeech, Voice


class TextToSpeechElevenLabs(TextToSpeech):
    """
    text to speech implementation using the elevenlabs api.

    requires an api key to be set as the environment variable elevenlabs_api_key
    or passed during initialization.
    """

    def get_account_credits(self):
        """fetch and return current elevenlabs character usage, limit, and status."""
        if not self.client:
            logger().warning("elevenlabs client not initialized; cannot fetch credits.")
            return None
        try:
            subscription_info = self.client.user.get_subscription()
            return {
                "character_count": getattr(subscription_info, "character_count", None),
                "character_limit": getattr(subscription_info, "character_limit", None),
                "remaining_characters": getattr(subscription_info, "character_limit", 0)
                - getattr(subscription_info, "character_count", 0),
                "tier": getattr(subscription_info, "tier", None),
                "status": getattr(subscription_info, "status", None),
                "next_reset_unix": getattr(
                    subscription_info, "next_character_count_reset_unix", None
                ),
            }
        except Exception as e:
            logger().error(f"failed to fetch elevenlabs account credits: {e}")
            return None

    # define gender constants matching elevenlabs labels if possible
    _GENDER_FEMALE = "female"
    _GENDER_MALE = "male"
    _GENDER_UNKNOWN = "unknown"  # for voices where gender isn't clear

    # --- default voice ids ---
    # these are examples; replace with your preferred default elevenlabs voice ids
    # find voice ids via api or web ui: https://elevenlabs.io/voice-library
    DEFAULT_FEMALE_VOICE_ID = "21m00Tcm4TlvDq8ikWAM"  # common default, use actual id like "21m00Tcm4TlvDq8ikWAM" if needed
    DEFAULT_MALE_VOICE_ID = "UgBBYS2sOqTuMpoF3BR0"  # common default, use actual id like "pNInz6obpgDQGcFmaJgB" if needed
    DEFAULT_VOICE_ID = (
        DEFAULT_MALE_VOICE_ID  # overall default if gender unknown/not specified
    )

    def __init__(self, api_key: Optional[str] = None):
        """
        initialize the elevenlabs tts client.

        args:
            api_key: optional elevenlabs api key. if not provided, will try to get
                     from environment variable elevenlabs_api_key.
        """
        super().__init__()

        if not ELEVENLABS_AVAILABLE:
            raise ImportError(
                "elevenlabs sdk not found. please install it using: uv pip install elevenlabs"
            )

        self.api_key = api_key or os.environ.get("ELEVENLABS_API_KEY")
        self.client: Optional[ElevenLabs] = None  # add type hint
        self._voices_cache: Optional[List[Voice]] = None
        self._speed_warning_shown = False  # flag to show warning only once

        if not self.api_key:
            logger().warning(
                "elevenlabs api key not provided. set elevenlabs_api_key environment variable or pass api_key parameter."
            )
            logger().warning(
                "tts synthesis will fail. only listing voices/languages might work."
            )
        else:
            try:
                # initialize the client
                self.client = ElevenLabs(api_key=self.api_key)
                logger().info("successfully initialized elevenlabs client.")
                # pre-fetch voices on initialization if client is available
                self._fetch_and_cache_voices()
            except Exception as e:
                logger().error(f"failed to initialize elevenlabs client: {str(e)}")
                self.client = None  # ensure client is none on error

    def _fetch_and_cache_voices(self) -> List[Voice]:
        """fetches voices from the api and caches them."""
        if not self.client:
            logger().error("elevenlabs client not initialized. cannot fetch voices.")
            return []

        # only fetch if cache is empty or not yet populated
        if self._voices_cache is None:
            logger().debug("voice cache is empty, attempting to fetch from api...")
            self._voices_cache = []  # initialize cache before fetching
            try:
                self._fetch_voices_from_api()
            except Exception as e:
                self._handle_voice_fetch_error(e)
                # return empty list on fetch error
                return []
        else:
            logger().debug("returning cached voices.")

        # ensure we return a list even if caching failed internally
        return self._voices_cache if self._voices_cache is not None else []

    def _fetch_voices_from_api(self) -> None:
        """
        internal method to fetch voices from api and populate the cache.
        raises exceptions on failure.
        """
        if not self.client:
            raise ConnectionError("elevenlabs client not initialized.")
        if self._voices_cache is None:
            self._voices_cache = []  # ensure cache is initialized

        logger().debug("fetching voices from elevenlabs api...")
        api_voices = self.client.voices.get_all().voices  # this might raise ApiError

        # clear existing cache before populating
        self._voices_cache.clear()

        for voice in api_voices:
            # use the helper to determine gender consistently
            gender = self._determine_gender(voice)
            # extract accent if available
            region = voice.labels.get("accent", "") if voice.labels else ""

            # use voice.voice_id as the 'name' for our voice object, as it's the identifier
            self._voices_cache.append(
                Voice(name=voice.voice_id, gender=gender, region=region)
            )

        logger().info(f"fetched and cached {len(self._voices_cache)} voices.")

    def _handle_voice_fetch_error(self, error: Exception) -> None:
        """handle errors during voice fetching."""
        log_message = f"failed to fetch voices from elevenlabs api: {error}"
        if isinstance(error, ApiError):
            log_message = f"elevenlabs api error fetching voices (status: {error.status_code}): {error.body}"

        logger().error(log_message)
        # ensure cache is reset to none or empty on error
        self._voices_cache = None

    def _determine_gender(self, api_voice) -> str:
        """
        determine gender from elevenlabs voice information.

        args:
            api_voice: an elevenlabs voice object from the api response.

        returns:
            standardized gender string ('female', 'male', 'unknown').
        """
        # prefer labels if available
        if api_voice.labels and "gender" in api_voice.labels:
            gender_label = api_voice.labels["gender"].lower()
            if gender_label == self._GENDER_FEMALE:
                return self._GENDER_FEMALE
            if gender_label == self._GENDER_MALE:
                return self._GENDER_MALE

        # fallback heuristic based on name (less reliable)
        if api_voice.name:
            name_lower = api_voice.name.lower()
            if "female" in name_lower or "woman" in name_lower:
                return self._GENDER_FEMALE
            if "male" in name_lower or "man" in name_lower:
                return self._GENDER_MALE

        # default if no information found
        logger().debug(
            f"could not determine gender for voice id {api_voice.voice_id}, defaulting to '{self._GENDER_UNKNOWN}'."
        )
        return self._GENDER_UNKNOWN

    def get_available_voices(self, language_code: Optional[str] = None) -> List[Voice]:
        """
        return list of available voices.
        note: elevenlabs api v1 doesn't directly filter get_all() by language.
              this method returns all fetched voices. language filtering should
              happen based on model selection during synthesis if needed, or client-side.

        args:
            language_code: iso language code (e.g., 'en'). currently ignored for filtering.

        returns:
            list of voice objects available.
        """
        # fetch/return cached voices
        all_voices = self._fetch_and_cache_voices()

        if not all_voices:
            logger().warning("no voices found or failed to fetch voices.")
            return []

        # log that filtering isn't applied here
        if language_code:
            logger().debug(
                f"returning all {len(all_voices)} fetched voices. language code '{language_code}' ignored for filtering in this method."
            )
        else:
            logger().debug(f"returning all {len(all_voices)} fetched voices.")

        return all_voices

    def get_languages(self) -> List[str]:
        """
        return list of language codes potentially supported by elevenlabs.
        based on common models like multilingual v2. check elevenlabs docs for specifics.

        returns:
            list of iso language codes (both 639-1 and 639-3 formats supported).
        """
        # list based on known supported languages for models like multilingual v2
        # includes both iso 639-1 (2-letter) and iso 639-3 (3-letter) codes
        supported_languages = [
            # iso 639-1 codes (backward compatibility)
            "en",
            "ru",
            "uz",
            # iso 639-3 codes (preferred standard)
            "eng",
            "rus",
            "uzb",
        ]
        logger().debug(
            f"returning static list of potential elevenlabs languages: {supported_languages}"
        )
        return supported_languages

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        reraise=True,
    )
    def _synthesize_with_retry(self, text: str, voice_id: str, speed: float) -> bytes:
        """
        synthesize speech with retry logic using the elevenlabs client.

        args:
            text: the text to synthesize.
            voice_id: the specific elevenlabs voice id to use.
            speed: target speed (currently ignored by this implementation).

        returns:
            the synthesized audio data as bytes.

        raises:
            connectionerror: if the client is not initialized.
            valueerror: if the api returns empty audio data.
            apierror: if the api call fails after retries.
            exception: for other unexpected errors.
        """
        if not self.client:
            raise ConnectionError("elevenlabs client not initialized.")

        try:
            # log warning about ignored speed parameter only once
            if not self._is_default_speed(speed) and not self._speed_warning_shown:
                logger().warning(
                    f"elevenlabs tts currently ignores the speed parameter (requested: {speed:.1f}). using default speed."
                )
                self._speed_warning_shown = True

            # define default voice settings - consider making these configurable if needed
            # these settings are generally good defaults for multilingual v2
            voice_settings = VoiceSettings(
                stability=0.71,
                similarity_boost=0.5,
                style=0.0,  # style=0 is often recommended for multilingual models unless specific style needed
                use_speaker_boost=True,
            )

            logger().debug(
                f"synthesizing text with voice_id: {voice_id}, model: eleven_multilingual_v2"
            )
            # use the generate method which returns an iterator of bytes
            audio_iterator: Iterator[bytes] = self.client.generate(
                text=text,
                voice=voice_id,  # pass the selected voice_id
                model="eleven_multilingual_v2",  # consider making model configurable
                voice_settings=voice_settings,
                # output_format defaults to mp3_44100_128, specify if different needed
            )

            # concatenate the audio chunks from the iterator
            audio_bytes = b"".join(audio_iterator)

            if not audio_bytes:
                # this case might indicate an issue upstream or an empty generation
                raise ValueError("elevenlabs api returned empty audio data.")

            logger().debug(f"received {len(audio_bytes)} bytes of audio data.")
            return audio_bytes

        except ApiError as e:
            # log specific api errors
            logger().error(
                f"elevenlabs api error during synthesis (status: {e.status_code}): {e.body}"
            )
            raise  # re-raise for tenacity to handle retries
        except Exception as e:
            # catch other potential errors during generation or concatenation
            logger().error(f"unexpected error during elevenlabs synthesis: {e}")
            raise  # re-raise unexpected errors

    def _convert_text_to_speech_primary(
        self,
        *,
        assigned_voice: str,  # this is the voice_id for elevenlabs
        target_language: str,  # currently ignored by this implementation
        output_filename: str,
        text: str,
        speed: float,
    ) -> str:
        """
        convert text to speech using the elevenlabs api.

        args:
            assigned_voice: the elevenlabs voice id to use.
            target_language: iso language code (ignored for now).
            output_filename: path to save the generated mp3 audio file.
            text: the text content to convert to speech.
            speed: desired speech rate (currently ignored by this implementation).

        returns:
            path to the created audio file (or fallback silent file on error).
        """
        logger().debug(
            f"texttospeechelevenlabs._convert_text_to_speech: synthesizing '{text[:50]}...' with voice '{assigned_voice}'"
        )

        if not self.client:
            logger().error(
                "elevenlabs client is not initialized. cannot synthesize speech."
            )
            return self._create_fallback_audio(output_filename)

        # ensure text is not empty
        if not text or not text.strip():
            logger().warning("empty text provided for tts, generating silence.")
            return self._create_fallback_audio(output_filename)

        try:
            # call the synthesis method with retry logic
            audio_bytes = self._synthesize_with_retry(
                text=text, voice_id=assigned_voice, speed=speed
            )

            # save the received audio bytes to the specified file
            # using the imported elevenlabs_save utility
            elevenlabs_save(audio=audio_bytes, filename=output_filename)

            # verify that the file was created and is not empty
            if (
                not os.path.exists(output_filename)
                or os.path.getsize(output_filename) < 100
            ):  # check for minimal size
                logger().error(
                    f"failed to save audio to {output_filename} or file is too small."
                )
                # attempt to create fallback if saving failed
                return self._create_fallback_audio(output_filename)

            logger().debug(
                f"successfully saved synthesized audio to: '{output_filename}'"
            )
            return output_filename

        except Exception as e:
            # catch errors from _synthesize_with_retry (including retries) or save
            logger().error(f"failed to synthesize or save speech with elevenlabs: {e}")
            # create a fallback silent audio if any step failed
            return self._create_fallback_audio(output_filename)

    def _create_fallback_audio(
        self, output_filename: str, duration_seconds: float = 0.5
    ) -> str:
        """creates a short, silent fallback mp3 audio file."""
        try:
            logger().warning(f"creating silent fallback audio file: {output_filename}")
            # standard sample rate for compatibility
            sampling_rate = 44100
            # generate silent audio data
            num_samples = int(sampling_rate * duration_seconds)
            silent_data = np.zeros(num_samples, dtype=np.int16)

            # ensure the output directory exists
            output_dir = os.path.dirname(os.path.abspath(output_filename))
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # write as wav first (more reliable across platforms)
            temp_wav_file = output_filename.replace(".mp3", "_temp_fallback.wav")
            scipy.io.wavfile.write(temp_wav_file, rate=sampling_rate, data=silent_data)

            # convert wav to mp3 using pydub
            if os.path.exists(temp_wav_file):
                try:
                    audio_segment = AudioSegment.from_wav(temp_wav_file)
                    # export with a low bitrate for silence
                    audio_segment.export(output_filename, format="mp3", bitrate="32k")
                    logger().debug(f"exported silent mp3 to {output_filename}")
                except Exception as pydub_err:
                    logger().error(
                        f"pydub failed to convert silent wav to mp3: {pydub_err}"
                    )
                    # if conversion fails, try creating an empty mp3 as last resort
                    with open(output_filename, "wb") as f:
                        f.write(b"")
                finally:
                    # clean up the temporary wav file
                    if os.path.exists(temp_wav_file):
                        try:
                            os.remove(temp_wav_file)
                        except OSError:
                            pass
            else:
                logger().error("failed to create temporary silent wav file.")
                # create an empty mp3 if wav creation failed
                with open(output_filename, "wb") as f:
                    f.write(b"")

            # verify the final fallback file exists
            if not os.path.exists(output_filename):
                logger().error("failed to create even an empty fallback mp3 file.")
                # return the intended filename even if creation failed completely
                return output_filename

            return output_filename
        except Exception as e:
            logger().error(f"failed to create fallback audio: {e}")
            # return the intended filename as a last resort
            return output_filename

    def _does_voice_supports_speeds(self) -> bool:
        """
        check if the tts engine supports speed adjustments.
        elevenlabs v1 api does not have a direct speed parameter in the main
        tts endpoint or standard voicesettings.
        """
        logger().debug(
            "elevenlabs does not support direct speed control via api parameters in this implementation."
        )
        return False  # return false as direct speed parameter is not used.

    def _is_default_speed(self, speed: float) -> bool:
        """check if the speed is close to the default value (1.0)."""
        # use a small tolerance for floating point comparisons
        epsilon = 0.01
        return abs(speed - 1.0) < epsilon

    def cleanup(self):
        """clean up resources, specifically the httpx client if initialized."""
        logger().debug("cleaning up texttospeechelevenlabs resources...")
        self._close_httpx_client()
        self.client = None  # clear client reference

    def _close_httpx_client(self):
        # handle closing httpx client with reduced nesting
        client_wrapper = getattr(self.client, "_client_wrapper", None)
        if not client_wrapper:
            return
        http_client = getattr(client_wrapper, "httpx_client", None)
        if not http_client or not hasattr(http_client, "close"):
            return
        try:
            # check for async client's aclose method
            if hasattr(http_client, "aclose") and callable(http_client.aclose):
                # note: cannot reliably call async close here. rely on atexit or context manager.
                logger().debug("async httpx client found; relying on external cleanup.")
                return
            if callable(http_client.close):
                http_client.close()
                logger().info("closed elevenlabs sync httpx client.")
        except Exception as e:
            logger().warning(f"error closing elevenlabs httpx client: {e}")
