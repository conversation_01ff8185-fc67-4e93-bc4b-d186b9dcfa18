import math
import os
from abc import ABC, abstractmethod
from typing import Final, List, Mapping, NamedTuple, Sequence

from pydub import AudioSegment

from src.utils.logger import logger
from src.video_processing.ffmpeg import FFmpeg
from src.utils.utterance import Utterance


class Voice(NamedTuple):
    name: str
    gender: str
    region: str = ""


class TextToSpeech(ABC):
    def __init__(self):
        self._SSML_MALE: Final[str] = "Male"
        self._SSML_FEMALE: Final[str] = "Female"
        self._DEFAULT_SPEED: Final[float] = 1.0

    @abstractmethod
    def get_available_voices(self, language_code: str) -> List[Voice]:
        pass

    def get_voices_for_region_only(
        self, *, voices: List[Voice], target_language_region: str
    ) -> List[Voice]:
        if len(target_language_region) == 0:
            return voices

        voices_copy = []

        for voice in voices:
            if voice.region.endswith(target_language_region):
                voices_copy.append(voice)

        return voices_copy

    def assign_voices(
        self,
        *,
        utterance_metadata: Sequence[Mapping[str, str | float]],
        target_language: str,
        target_language_region: str,
    ) -> Mapping[str, str | None]:
        voice_assignment = {}
        # store fallback provider instance for reuse
        self._fallback_tts_instance = None

        for chunk in utterance_metadata:
            speaker_id = chunk["speaker_id"]
            if speaker_id in voice_assignment:
                continue
            gender = chunk["gender"]
            voice_result = self._select_voice_by_gender_with_fallback(
                primary_tts=self,
                gender=gender,
                language_code=target_language,
                fallback_voice=None,
            )

            # extract just the voice name for backward compatibility
            if isinstance(voice_result, dict):
                voice_assignment[speaker_id] = voice_result.get("voice")
            else:
                voice_assignment[speaker_id] = voice_result

        logger().info(f"text_to_speech.assign_voices. returns: {voice_assignment}")
        return voice_assignment

    def _create_fallback_tts_provider(self) -> "TextToSpeech":
        """create azure tts provider as fallback when primary provider lacks voices."""
        try:
            from src.audio_processing.text_to_speech_azure import TextToSpeechAzure
            import os

            # try both uppercase and lowercase environment variable names
            azure_speech_key = os.environ.get("AZURE_SPEECH_KEY") or os.environ.get(
                "azure_speech_key"
            )
            azure_region = os.environ.get("AZURE_SPEECH_REGION") or os.environ.get(
                "azure_region"
            )

            if not azure_speech_key or not azure_region:
                logger().warning(
                    "azure credentials not available for fallback. set azure_speech_key and azure_speech_region environment variables."
                )
                return None

            fallback_tts = TextToSpeechAzure(
                subscription_key=azure_speech_key, region=azure_region
            )
            logger().info("created azure tts as fallback provider")
            return fallback_tts

        except ImportError as e:
            logger().error(f"failed to import azure tts for fallback: {e}")
            return None
        except Exception as e:
            logger().error(f"failed to create fallback tts provider: {e}")
            return None

    def _select_voice_by_gender_with_fallback(
        self, primary_tts, gender: str, language_code: str, fallback_voice: str | None
    ) -> str | None:
        """select a voice matching the specified gender, with provider fallback."""
        # first try the primary provider
        selected_voice = self._select_voice_by_gender(
            tts=primary_tts,
            gender=gender,
            language_code=language_code,
            fallback_voice="",
        )

        # if primary provider found a voice, use it
        if selected_voice and selected_voice != "":
            return selected_voice

        # if primary provider failed, try fallback provider
        logger().info(
            f"primary tts provider has no {gender} voice for {language_code}, trying fallback provider"
        )

        # create or reuse fallback provider
        if (
            not hasattr(self, "_fallback_tts_instance")
            or self._fallback_tts_instance is None
        ):
            self._fallback_tts_instance = self._create_fallback_tts_provider()

        if self._fallback_tts_instance is None:
            logger().warning(
                "no fallback provider available, using any available voice from primary provider"
            )
            # try to get any available voice from primary provider as last resort
            try:
                available_voices = primary_tts.get_available_voices(language_code)
                if available_voices:
                    default_voice = available_voices[0].name
                    logger().info(
                        f"using default voice '{default_voice}' from primary provider for {language_code}"
                    )
                    return default_voice
            except Exception as e:
                logger().error(
                    f"error getting default voice from primary provider: {e}"
                )

            # if all else fails, return a hardcoded default to prevent None
            logger().warning(
                f"no voices available for {language_code}, using hardcoded fallback"
            )
            return "nigora"  # yandex uzbek female voice as last resort

        fallback_voice_result = self._select_voice_by_gender(
            tts=self._fallback_tts_instance,
            gender=gender,
            language_code=language_code,
            fallback_voice="",
        )

        if fallback_voice_result and fallback_voice_result != "":
            logger().info(
                f"fallback provider found {gender} voice '{fallback_voice_result}' for {language_code}"
            )
            # mark this voice as requiring fallback provider
            self._mark_voice_as_fallback(fallback_voice_result)
            return fallback_voice_result
        else:
            logger().warning(
                f"both primary and fallback providers failed to find {gender} voice for {language_code}"
            )
            # try to get any available voice from fallback provider as last resort
            try:
                available_voices = self._fallback_tts_instance.get_available_voices(
                    language_code
                )
                if available_voices:
                    default_voice = available_voices[0].name
                    logger().info(
                        f"using default voice '{default_voice}' from fallback provider for {language_code}"
                    )
                    # mark this voice as requiring fallback provider
                    self._mark_voice_as_fallback(default_voice)
                    return default_voice
            except Exception as e:
                logger().error(
                    f"error getting default voice from fallback provider: {e}"
                )

            # final fallback to prevent None
            logger().warning(
                f"all fallback attempts failed for {language_code}, using hardcoded voice"
            )
            return "nigora"  # yandex uzbek female voice as absolute last resort

    def _mark_voice_as_fallback(self, voice_name: str):
        """mark a voice as requiring the fallback provider for synthesis."""
        if not hasattr(self, "_fallback_voices"):
            self._fallback_voices = set()
        self._fallback_voices.add(voice_name)

    def _is_fallback_voice(self, voice_name: str) -> bool:
        """check if a voice requires the fallback provider for synthesis."""
        if not hasattr(self, "_fallback_voices"):
            return False
        return voice_name in self._fallback_voices

    def _select_voice_by_gender(
        self, tts, gender: str, language_code: str, fallback_voice: str
    ) -> str:
        """select a voice matching the specified gender, with provider-aware fallback."""
        # force elevenlabs to use hardcoded default ids for gender-based selection
        try:
            from src.audio_processing.text_to_speech_elevenlabs import (
                TextToSpeechElevenLabs,
            )

            # only log provider class at debug level
            tts_class = type(tts)
            logger().debug(
                f"tts provider class: {tts_class} (isinstance: {isinstance(tts, TextToSpeechElevenLabs)})"
            )
            if tts.__class__.__name__ == "TextToSpeechElevenLabs" or isinstance(
                tts, TextToSpeechElevenLabs
            ):
                if gender == getattr(tts, "_GENDER_FEMALE", "female"):
                    return getattr(tts, "DEFAULT_FEMALE_VOICE_ID", fallback_voice)
                elif gender == getattr(tts, "_GENDER_MALE", "male"):
                    return getattr(tts, "DEFAULT_MALE_VOICE_ID", fallback_voice)
                else:
                    return getattr(tts, "DEFAULT_VOICE_ID", fallback_voice)
        except ImportError:
            # elevenlabs module not available, continue with general logic
            pass

        # fallback to original logic for other providers
        if not gender:
            return fallback_voice
        try:
            available_voices = tts.get_available_voices(language_code)
            if not available_voices:
                logger().warning(
                    f"no voices available for {language_code} to select by gender."
                )
                return fallback_voice
            matching_voices = [
                v for v in available_voices if v.gender.lower() == gender.lower()
            ]
            if not matching_voices:
                logger().warning(
                    f"no voices found matching gender '{gender}' for {language_code}."
                )
                return fallback_voice
            selected_voice = matching_voices[0].name
            return selected_voice
        except Exception as e:
            logger().error(f"error selecting voice by gender: {e}")
            return fallback_voice

    def _convert_to_mp3(self, input_file, output_mp3):
        FFmpeg(FFmpeg.get_recommended_hwaccel()).convert_to_format(
            source=input_file, target=output_mp3
        )
        os.remove(input_file)

    def _add_text_to_speech_properties(
        self,
        *,
        utterance_metadata: Mapping[str, str | float],
    ) -> Mapping[str, str | float]:
        """updates utterance metadata with text-to-speech properties."""
        utterance_metadata_copy = utterance_metadata.copy()
        voice_properties = dict(
            speed=self._DEFAULT_SPEED,
        )
        utterance_metadata_copy.update(voice_properties)
        return utterance_metadata_copy

    def update_utterance_metadata(
        self,
        *,
        utterance: Utterance | None = None,
        utterance_metadata: Sequence[Mapping[str, str | float]],
        assigned_voices: Mapping[str, str] | None,
    ) -> Sequence[Mapping[str, str | float]]:
        """updates utterance metadata with assigned voices."""
        updated_utterance_metadata = []
        for metadata_item in utterance_metadata:
            new_utterance = metadata_item.copy()

            fields = (
                utterance.get_modified_utterance_fields(new_utterance)
                if utterance
                else []
            )
            # if "assigned_voice" has changed we give it priority and not overwrite it
            # by recalculating from speaker_id/gender
            if not utterance or (
                "speaker_id" in fields and "assigned_voice" not in fields
            ):
                speaker_id = new_utterance.get("speaker_id")
                new_utterance["assigned_voice"] = assigned_voices.get(speaker_id)
                new_utterance = self._add_text_to_speech_properties(
                    utterance_metadata=new_utterance
                )
            updated_utterance_metadata.append(new_utterance)
        return updated_utterance_metadata

    @abstractmethod
    def get_languages(self):
        pass

    """ tts add silence at the end that we want to remove to prevent increasing the speech of next
        segments if is not necessary."""

    def _convert_text_to_speech_without_end_silence(
        self,
        *,
        assigned_voice: str,
        target_language: str,
        output_filename: str,
        text: str,
        speed: float,
    ) -> str:
        dubbed_file = self._convert_text_to_speech(
            assigned_voice=assigned_voice,
            target_language=target_language,
            output_filename=output_filename,
            text=text,
            speed=speed,
        )

        dubbed_audio = AudioSegment.from_file(dubbed_file)
        pre_duration = len(dubbed_audio)

        FFmpeg(FFmpeg.get_recommended_hwaccel()).remove_silence(filename=dubbed_file)
        dubbed_audio = AudioSegment.from_file(dubbed_file)
        post_duration = len(dubbed_audio)
        if pre_duration != post_duration:
            logger().debug(
                f"text_to_speech._convert_text_to_speech_without_end_silence. file {dubbed_file} shorten from {pre_duration} to {post_duration}"
            )

        return dubbed_file

    def _convert_text_to_speech(
        self,
        *,
        assigned_voice: str,
        target_language: str,
        output_filename: str,
        text: str,
        speed: float,
    ) -> str:
        """convert text to speech, using fallback provider if needed."""
        # check if this voice requires fallback provider
        if self._is_fallback_voice(assigned_voice):
            if (
                hasattr(self, "_fallback_tts_instance")
                and self._fallback_tts_instance is not None
            ):
                logger().info(f"using fallback provider for voice '{assigned_voice}'")
                return self._fallback_tts_instance._convert_text_to_speech_primary(
                    assigned_voice=assigned_voice,
                    target_language=target_language,
                    output_filename=output_filename,
                    text=text,
                    speed=speed,
                )
            else:
                logger().warning(
                    f"fallback provider not available for voice '{assigned_voice}', using primary provider"
                )

        # use primary provider
        return self._convert_text_to_speech_primary(
            assigned_voice=assigned_voice,
            target_language=target_language,
            output_filename=output_filename,
            text=text,
            speed=speed,
        )

    @abstractmethod
    def _convert_text_to_speech_primary(
        self,
        *,
        assigned_voice: str,
        target_language: str,
        output_filename: str,
        text: str,
        speed: float,
    ) -> str:
        """primary tts implementation - to be implemented by subclasses."""
        pass

    def _calculate_target_utterance_speed(
        self,
        *,
        start: float,
        end: float,
        dubbed_file: str,
        utterance_metadata: Sequence[Mapping[str, float | str]],
        audio_file=str,
    ) -> float:
        """returns the ratio between the reference and target duration."""

        end = self.get_start_time_of_next_speech_utterance(
            utterance_metadata=utterance_metadata,
            start=start,
            end=end,
            audio_file=audio_file,
        )

        reference_length = end - start
        dubbed_audio = AudioSegment.from_file(dubbed_file)
        dubbed_duration = dubbed_audio.duration_seconds
        r = (
            math.ceil(dubbed_duration / reference_length * 10) / 10
        )  # rounds up with .1 decimal precision
        logger().debug(f"text_to_speech._calculate_target_utterance_speed: {r}")
        return r

    def _does_voice_supports_speeds(self):
        return False

    def get_start_time_of_next_speech_utterance(
        self,
        *,
        utterance_metadata: Sequence[Mapping[str, str | float]],
        start: float,
        end: float,
        audio_file: str,
    ) -> float:
        result = None
        for utterance in utterance_metadata:
            u_start = utterance["start"]
            if u_start <= start:
                continue

            for_dubbing = utterance["for_dubbing"]
            if not for_dubbing:
                continue

            result = u_start
            break

        if not result:
            try:
                background_audio = AudioSegment.from_mp3(audio_file)
                total_duration = background_audio.duration_seconds
                logger().debug(
                    f"get_start_time_of_next_speech_utterance. file duration: {total_duration}"
                )
                result = total_duration
            except Exception as e:
                logger().error(f"error '{e}' reading {audio_file}")

        if not result:
            result = end

        logger().debug(
            f"get_start_time_of_next_speech_utterance from_time: {start}, result: {result}"
        )
        return result

    def dub_utterances(
        self,
        *,
        utterance_metadata: Sequence[Mapping[str, str | float]],
        output_directory: str,
        target_language: str,
        audio_file: str,
        modified_metadata: Sequence[Mapping[str, str | float]] | None = None,
    ) -> Sequence[Mapping[str, str | float]]:
        """processes a list of utterance metadata, generating dubbed audio files."""

        modified_ids = {}
        if modified_metadata is not None:
            modified_ids = {utterance["id"] for utterance in modified_metadata}

        updated_utterance_metadata = []
        for utterance in utterance_metadata:
            if modified_metadata is not None and utterance["id"] not in modified_ids:
                utterance_copy = utterance.copy()
                updated_utterance_metadata.append(utterance_copy)
                continue

            utterance_copy = utterance.copy()
            if not utterance_copy["for_dubbing"]:
                try:
                    dubbed_path = utterance_copy["path"]
                except KeyError:
                    dubbed_path = f"chunk_{utterance['start']}_{utterance['end']}.mp3"
            else:
                assigned_voice = utterance_copy["assigned_voice"]
                text = utterance_copy["translated_text"]
                try:
                    path = utterance_copy["path"]
                    base_filename = os.path.splitext(os.path.basename(path))[0]
                    output_filename = os.path.join(
                        output_directory, f"dubbed_{base_filename}.mp3"
                    )
                except KeyError:
                    output_filename = os.path.join(
                        output_directory,
                        f"dubbed_chunk_{utterance['start']}_{utterance['end']}.mp3",
                    )

                speed = utterance_copy["speed"]
                dubbed_path = self._convert_text_to_speech_without_end_silence(
                    assigned_voice=assigned_voice,
                    target_language=target_language,
                    output_filename=output_filename,
                    text=text,
                    speed=speed,
                )
                assigned_voice = utterance_copy.get("assigned_voice", None)
                assigned_voice = assigned_voice if assigned_voice else ""
                support_speeds = self._does_voice_supports_speeds()

                start = utterance["start"]
                end = utterance["end"]
                speed = self._calculate_target_utterance_speed(
                    start=start,
                    end=end,
                    dubbed_file=dubbed_path,
                    utterance_metadata=utterance_metadata,
                    audio_file=audio_file,
                )

                logger().debug(f"support_speeds: {support_speeds}, speed: {speed}")

                if speed > 1.0:
                    translated_text = utterance_copy["translated_text"]
                    logger().debug(
                        f"text_to_speech.dub_utterances. need to increase speed for '{translated_text}'"
                    )

                    MAX_SPEED = 1.3
                    if speed > MAX_SPEED:
                        logger().debug(
                            f"text_to_speech.dub_utterances: reduced speed from {speed} to {MAX_SPEED}"
                        )
                        speed = MAX_SPEED

                    translated_text = utterance_copy["translated_text"]
                    logger().debug(
                        f"text_to_speech.dub_utterances: adjusting speed to {speed} for '{translated_text}'"
                    )

                    utterance_copy["speed"] = speed
                    if support_speeds:
                        dubbed_path = self._convert_text_to_speech_without_end_silence(
                            assigned_voice=assigned_voice,
                            target_language=target_language,
                            output_filename=output_filename,
                            text=text,
                            speed=speed,
                        )
                    else:
                        FFmpeg(FFmpeg.get_recommended_hwaccel()).adjust_audio_speed(
                            filename=dubbed_path,
                            speed=speed,
                        )
                        logger().debug(
                            f"text_to_speech.adjust_audio_speed: dubbed_audio: {dubbed_path}, speed: {speed}"
                        )
                else:
                    utterance_copy["speed"] = self._DEFAULT_SPEED

            utterance_copy["dubbed_path"] = dubbed_path
            updated_utterance_metadata.append(utterance_copy)
        return updated_utterance_metadata
