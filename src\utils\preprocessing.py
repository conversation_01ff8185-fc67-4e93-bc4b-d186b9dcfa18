import dataclasses


@dataclasses.dataclass
class PreprocessingArtifacts:
    """instance with preprocessing outputs.

    attributes:
        video_file: a path to a video ad with no audio.
        audio_file: a path to an audio track from the ad.
        audio_vocals_file: a path to an audio track with vocals only.
        audio_background_file: a path to and audio track from the ad with removed
          vocals.
    """

    video_file: str | None
    audio_file: str
    audio_vocals_file: str | None = None
    audio_background_file: str | None = None
