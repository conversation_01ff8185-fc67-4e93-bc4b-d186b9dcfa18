# style guide

> this file documents the style guidelines for the aizen platform server codebase.

## code style

-   all python code should follow [pep 8](https://peps.python.org/pep-0008/) style guide
-   all logging messages and print-like features must be in lowercase
-   comments in code must always be in lowercase
-   conventional commits preferred

## documentation style

-   markdown docs must be in lowercase
-   docstrings should follow [google style](https://google.github.io/styleguide/pyguide.html#38-comments-and-docstrings)

## naming conventions

-   use snake_case for variables, functions, and file names
-   use pascalcase for class names
-   constants should be in uppercase with underscores

## file organization

_file organization conventions to be defined_
