# feature archive: unified run configuration

## summary

a centralized json configuration system (`run_config.json`) has replaced scattered hard-coded parameters throughout the aizen platform. the new `src/config.py` module defines a nested pydantic `RunConfig` schema and a `load_config` helper. cli integration via the `--config` flag and runtime propagation in `dubber` and downstream modules enable clean, type-safe configuration management.

## date completed

2025-06-20

## key files added / modified

-   `src/config.py` (new)
-   `run_config.json` (example configuration)
-   `src/core/command_line.py` (added `--config` flag and loader integration)
-   `src/core/dubbing.py` (accept `RunConfig` and apply settings)
-   `README.md` (documentation section for unified config)

## requirements addressed

-   single source of configuration truth across the entire pipeline.
-   runtime validation of user-supplied configuration files.
-   backwards-compatible overrides via traditional cli flags.

## implementation details

1. **schema design** – nested pydantic models (`InputProcessingConfig`, `TranslationConfig`, `PerformanceConfig`, `ModelConfig`, `OutputConfig`) combined into a top-level `RunConfig`.
2. **loader helper** – `load_config(path)` in `src/config.py` reads json and validates against the schema, raising clear errors on failure.
3. **cli extension** – `--config` argument loads the file early and stores the result on the parsed args for later use. individual flags override config values when present.
4. **pipeline wiring** – `dubber` constructor accepts an optional `RunConfig`; segmentation, performance, and tts parameters now default to config values when explicit args are absent.
5. **documentation** – readme updated with configuration section and example command.

## testing performed

-   manual end-to-end run using the provided example config with spanish target language.
-   regression run with legacy cli flags to confirm backwards compatibility.
-   code lints (ruff/black) and type checks (mypy) passed.

## lessons learned

see reflection document for detailed insights.

## related work

-   reflection: [reflection-unified-run-configuration.md](memory-bank/reflection/reflection-unified-run-configuration.md)

## notes

unit tests for `load_config` are planned as a follow-up task.
