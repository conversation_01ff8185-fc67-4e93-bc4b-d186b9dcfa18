# aizen platform - ai dubbing system environment configuration
# copy this file to .env and fill in your actual api keys and credentials

# ============================================================================
# yandex speechkit (text-to-speech)
# ============================================================================
# option 1: oauth token and folder id (legacy method)
YANDEX_OAUTH_TOKEN=your_oauth_token_here
YANDEX_FOLDER_ID=your_folder_id_here

# option 2: iam token (preferred method)
YANDEX_IAM_TOKEN=your_iam_token_here

# ============================================================================
# azure cognitive services (text-to-speech)
# ============================================================================
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_region_here

# ============================================================================
# elevenlabs (text-to-speech)
# ============================================================================
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# ============================================================================
# assemblyai (speech-to-text)
# ============================================================================
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here

# ============================================================================
# google services (translation)
# ============================================================================
GOOGLE_API_KEY=your_google_gemini_api_key_here

# ============================================================================
# hugging face (for pyannote speaker diarization)
# ============================================================================
HF_TOKEN=your_hugging_face_token_here

# ============================================================================
# optional: pyannote authentication (alternative to hf_token)
# ============================================================================
PYANNOTE_AUTH_TOKEN=your_pyannote_token_here

# ============================================================================
# development and debugging
# ============================================================================
# uncomment to enable debug logging
# LOG_LEVEL=debug

# uncomment to set custom output directory
# OUTPUT_DIRECTORY=./custom_outputs

# ============================================================================
# notes:
# ============================================================================
# 1. yandex speechkit: get credentials from https://cloud.yandex.com/
# 2. azure speech: get credentials from azure portal
# 3. elevenlabs: get api key from https://elevenlabs.io/
# 4. assemblyai: get api key from https://www.assemblyai.com/
# 5. google gemini: get api key from google ai studio
# 6. hugging face: get token from https://huggingface.co/settings/tokens
# 7. for pyannote models, accept user agreements at:
#    - https://huggingface.co/pyannote/segmentation-3.0
#    - https://huggingface.co/pyannote/speaker-diarization-3.1
