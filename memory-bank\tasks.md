# tasks

> this file is used for active, in-progress task tracking, detailing steps, checklists, and component lists.

## completed tasks

-   **bug fix: ensure separated audio/video files are saved inside run output directory**

    -   **status:** completed & archived
    -   **archive:** [archive-filepath-safety.md](memory-bank/archive/archive-filepath-safety.md)

-   **feature: unified run configuration**
    -   **status:** completed & archived
    -   **archive:** [archive-unified-run-configuration.md](memory-bank/archive/archive-unified-run-configuration.md)

## current task

**enhancement: improve demucs progress visibility and processing verification**

-   **status:** planning (level 2 – simple enhancement)

### overview of changes

users report that demucs audio separation appears to finish suspiciously fast and shows no progress output in the console. we need to ensure that:

1. demucs is invoked with the configured quality parameters (model `htdemucs_ft`, `shifts=10`, etc.).
2. real-time progress from demucs is streamed to the logger so users can monitor separation progress.
3. runtime metrics are recorded to validate that the expected processing time scales with audio length and quality settings.

### files to modify / create

-   `src/audio_processing/demucs.py`
-   `src/utils/logger.py` (minor: add verbose progress category if needed)
-   `memory-bank/progress.md` (runtime snapshots – no code change needed)

### implementation steps

1. **verify command construction**
    - add unit log to confirm the exact demucs command (model, shifts, overlap).
2. **stream demucs output**
    - replace `subprocess.run(... capture_output=True ...)` with `subprocess.Popen`.
    - pipe both stdout and stderr line-by-line and forward to `logger().info` to show progress bars.
3. **measure execution time**
    - log start/end timestamps around separation to track duration.
4. **add `--progress` flag** (demucs shows progress on stderr by default; ensure we are not hiding it).
5. **refactor execute_demucs_command**
    - wrap in try/except and surface errors immediately.
6. **testing strategy**
    - run `python -m src.core.main --config run_config.json` on a 30-second clip and expect visible progress.
    - check that runtime increases when `shifts` is bumped from 1 to 10.

### potential challenges

| challenge                      | mitigation                                                         |
| ------------------------------ | ------------------------------------------------------------------ |
| buffering hides live output    | use `universal_newlines=True` and `line_buffering=True` in `Popen` |
| progress bars may clutter logs | add `quiet` param toggle; default verbose only in debug level      |
| windows encoding issues        | ensure utf-8 decode errors are ignored (`errors="replace"`)        |

### testing checklist

-   [ ] visible demucs progress appears in console
-   [ ] separation output files exist and contain audio
-   [ ] runtime roughly proportional to model size and shifts
-   [ ] no regression in other audio processing stages

### notes

creative phase **not** required; enhancement is limited to subprocess handling and logging.

## checklist for next task

-   [ ] analyze requirements
-   [ ] determine complexity level
-   [ ] create implementation plan
-   [ ] implement solution
-   [ ] verify implementation
-   [ ] update documentation
-   [ ] create tests as needed
-   [ ] finalize and archive

## notes

van mode initialized. platform detected as windows 10. memory bank structure verified.
system is ready for next task assignment.
